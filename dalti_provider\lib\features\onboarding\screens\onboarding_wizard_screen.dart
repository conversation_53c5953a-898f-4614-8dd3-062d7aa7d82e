import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/theme_provider.dart';
import '../../../core/routing/app_routes.dart';
import '../../../core/services/local_storage_service.dart';
import '../models/onboarding_models.dart';
import '../providers/onboarding_provider.dart';
import '../controllers/wizard_controller.dart';
import '../widgets/wizard_stepper.dart';
import '../widgets/wizard_page.dart';
import '../steps/business_profile_step.dart';
import '../steps/location_setup_step.dart';
import '../steps/service_creation_step.dart';
import '../steps/queue_management_step.dart';
import '../steps/summary_step.dart';
import 'onboarding_completion_screen.dart';
import '../../../generated/l10n/app_localizations.dart';

/// Main onboarding wizard screen
class OnboardingWizardScreen extends ConsumerStatefulWidget {
  const OnboardingWizardScreen({super.key});

  @override
  ConsumerState<OnboardingWizardScreen> createState() =>
      _OnboardingWizardScreenState();
}

class _OnboardingWizardScreenState
    extends ConsumerState<OnboardingWizardScreen> {
  late OnboardingWizardController _wizardController;

  // Define the steps for the wizard
  static const List<OnboardingStep> _wizardSteps = [
    OnboardingStep.businessProfile,
    OnboardingStep.locationSetup,
    OnboardingStep.serviceCreation,
    OnboardingStep.queueManagement,
    OnboardingStep.summary,
  ];

  @override
  void initState() {
    super.initState();

    // Initialize wizard controller
    _wizardController = OnboardingWizardController(steps: _wizardSteps);

    // Load existing progress and set initial navigation state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingProgress();
      _updateNavigationState();
    });
  }

  @override
  void dispose() {
    _wizardController.dispose();
    super.dispose();
  }

  /// Load existing onboarding progress
  void _loadExistingProgress() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final data = onboardingState.data;

    if (data != null) {
      // Set wizard to current step
      _wizardController.goToStep(data.currentStep, animate: false);

      // Update completion state for each step
      for (final step in _wizardSteps) {
        _wizardController.markStepCompleted(step, data.isStepCompleted(step));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final onboardingState = ref.watch(onboardingNotifierProvider);

    // Update navigation state whenever onboarding data changes
    ref.listen(onboardingNotifierProvider, (previous, next) {
      if (previous?.data != next.data) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _updateNavigationState();
        });
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.businessSetup),
        backgroundColor:
            context.isDarkMode ? context.colors.surface : Colors.white,
        surfaceTintColor:
            context.isDarkMode ? context.colors.surface : Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: _showExitConfirmation,
        ),
        actions: [
          // Progress indicator in app bar
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Center(
              child: Text(
                '${_wizardController.currentIndex + 1}/${_wizardController.totalSteps}',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: context.colors.onSurfaceVariant,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Wizard stepper
          Container(
            color: context.colors.surface,
            child: Column(
              children: [
                // Step indicators
                AnimatedBuilder(
                  animation: _wizardController,
                  builder: (context, child) {
                    return WizardStepper(
                      currentStep: _wizardController.currentStep,
                      steps: _wizardSteps,
                      onStepTapped: _onStepTapped,
                    );
                  },
                ),

                // Progress bar
                AnimatedBuilder(
                  animation: _wizardController,
                  builder: (context, child) {
                    return WizardProgressIndicator(
                      progress: _wizardController.progress,
                      showPercentage: false,
                    );
                  },
                ),

                const SizedBox(height: 8),
              ],
            ),
          ),

          // Main content area
          Expanded(
            child: AnimatedBuilder(
              animation: _wizardController,
              builder: (context, child) {
                if (onboardingState.isLoading) {
                  return const WizardLoadingState(
                    message: 'Loading your progress...',
                  );
                }

                if (onboardingState.error != null) {
                  return WizardErrorState(
                    message: onboardingState.error!,
                    onRetry:
                        () =>
                            ref
                                .read(onboardingNotifierProvider.notifier)
                                .startOnboarding(),
                  );
                }

                return PageView(
                  controller: _wizardController.pageController,
                  onPageChanged: _onPageChanged,
                  physics:
                      const NeverScrollableScrollPhysics(), // Disable swipe gestures
                  children: _buildWizardPages(),
                );
              },
            ),
          ),

          // Navigation buttons
          AnimatedBuilder(
            animation: _wizardController,
            builder: (context, child) {
              return WizardNavigationButtons(
                canGoBack: _wizardController.canGoBack,
                canProceed: _wizardController.canProceed,
                isLoading: onboardingState.isSaving,
                nextButtonText: _getNextButtonText(),
                onNext: _onNext,
                onBack: _onBack,
              );
            },
          ),
        ],
      ),
    );
  }

  /// Build wizard pages
  List<Widget> _buildWizardPages() {
    return [
      BusinessProfileStep(controller: _wizardController),
      LocationSetupStep(controller: _wizardController),
      ServiceCreationStep(controller: _wizardController),
      QueueManagementStep(controller: _wizardController),
      SummaryStep(controller: _wizardController),
    ];
  }

  /// Handle step tapped in stepper
  void _onStepTapped(OnboardingStep step) {
    if (_wizardController.isStepAccessible(step)) {
      _wizardController.goToStep(step);
    } else {
      _showStepNotAccessibleMessage(step);
    }
  }

  /// Handle next button pressed
  void _onNext() async {
    final currentStep = _wizardController.currentStep;

    // Validate current step before proceeding
    if (await _validateCurrentStep()) {
      if (currentStep == OnboardingStep.summary) {
        // Complete onboarding
        await _completeOnboarding();
      } else {
        // Move to next step
        await _wizardController.nextStep();
        _updateNavigationState();
      }
    } else {
      // Show validation error if step is not valid
      _showValidationError();
    }
  }

  /// Handle back button pressed
  void _onBack() {
    _wizardController.previousStep();
    _updateNavigationState();
  }

  /// Handle page changed with validation
  void _onPageChanged(int index) {
    // Only allow page change if validation passes or going backwards
    final targetStep = _wizardController.steps[index];
    final currentIndex = _wizardController.currentIndex;

    if (index > currentIndex) {
      // Moving forward - validate current step first
      _validateCurrentStep().then((isValid) {
        if (!isValid) {
          // Invalid - return to current step
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _wizardController.pageController.animateToPage(
              currentIndex,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
            _showValidationError();
          });
          return;
        }
        // Valid - update controller state
        _wizardController.onPageChanged(index);
        _updateNavigationState();
      });
    } else {
      // Moving backwards or same page - always allowed
      _wizardController.onPageChanged(index);
      _updateNavigationState();
    }
  }

  /// Validate current step
  Future<bool> _validateCurrentStep() async {
    final currentStep = _wizardController.currentStep;
    final onboardingData = ref.read(onboardingNotifierProvider).data;

    if (onboardingData == null) return false;

    // Use the validation logic from onboarding provider
    return ref
        .read(onboardingNotifierProvider.notifier)
        .canProceedFromStep(currentStep, onboardingData);
  }

  /// Update navigation state based on current step validation
  void _updateNavigationState() async {
    final isValid = await _validateCurrentStep();
    _wizardController.updateNavigationState(canProceed: isValid);
  }

  /// Show validation error message
  void _showValidationError() {
    final currentStep = _wizardController.currentStep;
    String message;

    switch (currentStep) {
      case OnboardingStep.locationSetup:
        message =
            'Please fill in your city, address, and location coordinates before proceeding.';
        break;
      case OnboardingStep.serviceCreation:
        message = 'Please add at least one service before proceeding.';
        break;
      case OnboardingStep.queueManagement:
        message = 'Please add at least one queue before proceeding.';
        break;
      case OnboardingStep.businessProfile:
        message = 'Please complete your business profile before proceeding.';
        break;
      default:
        message = 'Please complete this step before proceeding.';
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: context.colors.error,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Complete onboarding process
  Future<void> _completeOnboarding() async {
    try {
      // Set completion flag to prevent any further async operations
      ref
          .read(onboardingNotifierProvider.notifier)
          .setCompletionInProgress(true);

      // Show loading state during completion
      setState(() {
        // This will show the loading state in navigation buttons
      });

      final completion =
          await ref
              .read(onboardingNotifierProvider.notifier)
              .completeOnboarding();

      if (completion != null && mounted && context.mounted) {
        print(
          '[OnboardingWizard] Onboarding completed successfully, navigating to dashboard',
        );
        // Show success message
        _showCompletionSuccess();

        // Navigate directly to dashboard
        context.go(AppRoutes.dashboard);
      } else {
        print(
          '[OnboardingWizard] Navigation skipped - completion: $completion, mounted: $mounted, context.mounted: ${context.mounted}',
        );
      }
    } catch (e) {
      if (mounted) {
        _showError('Failed to complete onboarding: $e');
      }
    }
  }

  /// Get next button text based on current step
  String _getNextButtonText() {
    switch (_wizardController.currentStep) {
      case OnboardingStep.summary:
        return 'Complete Setup';
      case OnboardingStep.completed:
        return 'Get Started';
      default:
        return 'Next';
    }
  }

  /// Show step not accessible message
  void _showStepNotAccessibleMessage(OnboardingStep step) {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Please complete the previous steps to access ${step.title}',
              ),
              backgroundColor: context.colors.error,
            ),
          );
        }
      });
    }
  }

  /// Show exit confirmation dialog with skip option
  void _showExitConfirmation() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Skip Setup?'),
            content: const Text(
              'You can skip the setup process and complete it later from your dashboard. Are you sure you want to skip?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();

                  // Save skip flag to local storage
                  await LocalStorageService.setOnboardingSkipped(true);

                  // Navigate to dashboard
                  context.go(AppRoutes.dashboard);
                },
                child: const Text('Skip Setup'),
              ),
            ],
          ),
    );
  }

  /// Show completion success message
  void _showCompletionSuccess() {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Text('Business setup completed successfully!'),
                ],
              ),
              backgroundColor: context.colors.primary,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  /// Show error message
  void _showError(String message) {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: context.colors.error,
            ),
          );
        }
      });
    }
  }
}

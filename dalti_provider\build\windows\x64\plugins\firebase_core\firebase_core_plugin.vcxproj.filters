﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin_c_api.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\messages.g.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include\firebase_core\firebase_core_plugin_c_api.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\messages.g.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated\firebase_core\plugin_version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{4A4641E1-3D11-3F45-B4CF-2C32B542F532}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{E9887D71-F5D0-3073-BB94-222B01CDC73C}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>

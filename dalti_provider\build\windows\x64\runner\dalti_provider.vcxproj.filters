﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\runner\flutter_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\runner\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\runner\utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\runner\win32_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\generated_plugin_registrant.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\runner\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\runner\Runner.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{E9887D71-F5D0-3073-BB94-222B01CDC73C}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>

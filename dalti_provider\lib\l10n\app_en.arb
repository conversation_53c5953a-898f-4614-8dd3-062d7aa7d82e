{"@@locale": "en", "@@last_modified": "2025-01-11T00:00:00.000Z", "appTitle": "<PERSON><PERSON>", "@appTitle": {"description": "The title of the application"}, "language": "Language", "@language": {"description": "Language label"}, "languageEnglish": "English", "@languageEnglish": {"description": "English language option"}, "languageFrench": "Français", "@languageFrench": {"description": "French language option"}, "languageArabic": "العربية", "@languageArabic": {"description": "Arabic language option"}, "settings": "Settings", "@settings": {"description": "Settings screen title"}, "profile": "Profile", "@profile": {"description": "Profile screen title"}, "dashboard": "Dashboard", "@dashboard": {"description": "Dashboard screen title"}, "appointments": "Appointments", "@appointments": {"description": "Appointments label"}, "appointmentDetails": "Appointment Details", "@appointmentDetails": {"description": "Appointment details screen title"}, "calendar": "Calendar", "@calendar": {"description": "Calendar screen title"}, "messages": "Messages", "@messages": {"description": "Messages screen title"}, "customers": "customers", "@customers": {"description": "Customers plural form"}, "services": "Services", "@services": {"description": "Services screen title"}, "locations": "Locations", "@locations": {"description": "Locations screen title"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "no": "No", "@no": {"description": "No button text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Error message title"}, "success": "Success", "@success": {"description": "Success message title"}, "languageChanged": "Language changed successfully", "@languageChanged": {"description": "Message shown when language is changed"}, "languageChangeError": "Failed to change language. Please try again.", "@languageChangeError": {"description": "Error message when language change fails"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Title for language selection dialog/screen"}, "currentLanguage": "Current Language", "@currentLanguage": {"description": "Label for current language display"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "register": "Register", "@register": {"description": "Register button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "firstName": "First Name", "@firstName": {"description": "First name field label"}, "lastName": "Last Name", "@lastName": {"description": "Last name field label"}, "phone": "Phone", "@phone": {"description": "Phone field label"}, "businessName": "Business Name", "@businessName": {"description": "Business name field label"}, "add": "Add", "@add": {"description": "Add button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "update": "Update", "@update": {"description": "Update button text"}, "create": "Create", "@create": {"description": "Create button text"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "filter": "Filter", "@filter": {"description": "Filter button text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button text"}, "back": "Back", "@back": {"description": "Back button text"}, "next": "Next", "@next": {"description": "Next button text"}, "previous": "Previous", "@previous": {"description": "Previous button text"}, "close": "Close", "@close": {"description": "Close button text"}, "done": "Done", "@done": {"description": "Done button text"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "noData": "No data available", "@noData": {"description": "Message when no data is available"}, "networkError": "Network error. Please check your connection.", "@networkError": {"description": "Network error message"}, "serverError": "Server error. Please try again later.", "@serverError": {"description": "Server error message"}, "unknownError": "An unknown error occurred.", "@unknownError": {"description": "Unknown error message"}, "validationError": "Please check your input and try again.", "@validationError": {"description": "Validation error message"}, "required": "This field is required", "@required": {"description": "Required field validation message"}, "invalidEmail": "Please enter a valid email address", "@invalidEmail": {"description": "Invalid email validation message"}, "invalidPhone": "Please enter a valid phone number", "@invalidPhone": {"description": "Invalid phone validation message"}, "passwordTooShort": "Password must be at least 6 characters", "@passwordTooShort": {"description": "Password length validation error"}, "passwordsDoNotMatch": "Passwords do not match", "@passwordsDoNotMatch": {"description": "Password mismatch validation error"}, "helpAndSupport": "Help & Support", "@helpAndSupport": {"description": "Help and support menu item"}, "theme": "Theme", "@theme": {"description": "Theme menu item"}, "themeLight": "Light", "@themeLight": {"description": "Light theme option"}, "themeDark": "Dark", "@themeDark": {"description": "Dark theme option"}, "themeSystem": "System", "@themeSystem": {"description": "System theme option"}, "logoutConfirmTitle": "Logout", "@logoutConfirmTitle": {"description": "Logout confirmation dialog title"}, "logoutConfirmMessage": "Are you sure you want to logout?", "@logoutConfirmMessage": {"description": "Logout confirmation dialog message"}, "editProfile": "Edit Profile", "@editProfile": {"description": "Edit profile tooltip and button text"}, "daltiProvider": "<PERSON><PERSON>", "@daltiProvider": {"description": "App name for default page titles"}, "gettingLocation": "Getting Location...", "@gettingLocation": {"description": "Getting location loading text"}, "useCurrentLocation": "Use Current Location", "@useCurrentLocation": {"description": "Button text to use current location"}, "notSpecified": "Not specified", "@notSpecified": {"description": "Text shown when a field value is not specified"}, "title": "Title", "@title": {"description": "Title field label"}, "category": "Category", "@category": {"description": "Category field label"}, "profileInformation": "Profile Information", "@profileInformation": {"description": "Profile information section title"}, "aboutMe": "About Me", "@aboutMe": {"description": "About me section title"}, "passwordResetSuccessful": "Password Reset Successful", "@passwordResetSuccessful": {"description": "Password reset success dialog title"}, "passwordResetSuccessMessage": "Your password has been reset successfully. You can now log in with your new password.", "@passwordResetSuccessMessage": {"description": "Password reset success dialog message"}, "goToLogin": "Go to Login", "@goToLogin": {"description": "Button text to go to login screen"}, "failedToResetPassword": "Failed to reset password. Please try again.", "@failedToResetPassword": {"description": "Error message when password reset fails"}, "forgotPassword": "Forgot Password", "@forgotPassword": {"description": "Forgot password link text"}, "resetPassword": "Reset Password", "@resetPassword": {"description": "Reset password title"}, "newPassword": "New Password", "@newPassword": {"description": "New password field label"}, "confirmNewPassword": "Confirm New Password", "@confirmNewPassword": {"description": "Confirm new password field label"}, "enterOtp": "Enter OTP", "@enterOtp": {"description": "OTP field label"}, "verifyOtp": "Verify OTP", "@verifyOtp": {"description": "Verify OTP button text"}, "resendOtp": "Resend OTP", "@resendOtp": {"description": "Resend OTP button text"}, "otpSent": "OTP sent to your email", "@otpSent": {"description": "Message when OTP is sent"}, "invalidOtp": "Invalid OTP. Please try again.", "@invalidOtp": {"description": "Error message for invalid OTP"}, "welcomeBack": "Welcome Back", "@welcomeBack": {"description": "Welcome message on login screen"}, "signInToContinue": "Sign in to continue", "@signInToContinue": {"description": "Subtitle on login screen"}, "dontHaveAccount": "Don't have an account?", "@dontHaveAccount": {"description": "Text before register link"}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {"description": "Already have account text"}, "signUp": "Sign Up", "@signUp": {"description": "Sign up button text"}, "signIn": "Sign In", "@signIn": {"description": "Sign in button text"}, "appointmentsList": "Appointments", "@appointmentsList": {"description": "Appointments list screen title"}, "notifications": "Notifications", "@notifications": {"description": "Notifications screen title"}, "loginSuccessful": "Login successful!", "@loginSuccessful": {"description": "Message shown when login is successful"}, "loginFailed": "<PERSON><PERSON> failed", "@loginFailed": {"description": "Message shown when login fails"}, "welcomeToDaltiProvider": "Welcome to <PERSON><PERSON> Pro<PERSON>r", "@welcomeToDaltiProvider": {"description": "Welcome message on login screen"}, "signInToManageBusiness": "Sign in to manage your business", "@signInToManageBusiness": {"description": "Subtitle on login screen"}, "getStarted": "Get Started", "@getStarted": {"description": "Get started button text"}, "skipSetup": "Skip Setup?", "@skipSetup": {"description": "Skip setup dialog title"}, "skipSetupMessage": "You can complete your business setup later from the dashboard. However, some features may be limited until setup is complete.", "@skipSetupMessage": {"description": "Skip setup dialog message"}, "continueSetup": "Continue Setup", "@continueSetup": {"description": "Continue setup button text"}, "skipForNow": "Skip for Now", "@skipForNow": {"description": "Skip for now button text"}, "startConversation": "Start Conversation", "@startConversation": {"description": "Start conversation button text"}, "failedToCreateConversation": "Failed to create conversation", "@failedToCreateConversation": {"description": "Error message when conversation creation fails"}, "selectCustomer": "Select a customer", "@selectCustomer": {"description": "Customer dropdown placeholder"}, "initialMessage": "Initial message (optional):", "@initialMessage": {"description": "Label for initial message field"}, "typeMessageHere": "Type your message here...", "@typeMessageHere": {"description": "Placeholder text for message input"}, "serviceColor": "Service Color", "@serviceColor": {"description": "Service color section title"}, "customerManagement": "Customer Management", "@customerManagement": {"description": "Customer management screen title"}, "comingSoon": "Coming Soon", "@comingSoon": {"description": "Coming soon placeholder text"}, "thisFeatureWillAllowYouTo": "This feature will allow you to:", "@thisFeatureWillAllowYouTo": {"description": "Feature description intro text"}, "basicInformation": "Basic Information", "@basicInformation": {"description": "Basic information section title"}, "contactInformation": "Contact Information", "@contactInformation": {"description": "Contact information section title"}, "phoneNumber": "Phone Number", "@phoneNumber": {"description": "Phone number field label"}, "nationalId": "National ID", "@nationalId": {"description": "National ID field label"}, "notes": "Notes", "@notes": {"description": "Notes field label"}, "firstNameRequired": "First name is required", "@firstNameRequired": {"description": "First name validation error"}, "lastNameRequired": "Last name is required", "@lastNameRequired": {"description": "Last name validation error"}, "pleaseEnterValidEmail": "Please enter a valid email", "@pleaseEnterValidEmail": {"description": "Valid email validation error"}, "pleaseEnterValidPhone": "Please enter a valid phone number", "@pleaseEnterValidPhone": {"description": "Phone validation error"}, "customerEmailHint": "<EMAIL>", "@customerEmailHint": {"description": "Email field hint text"}, "phoneNumberHint": "+213 123 456 789", "@phoneNumberHint": {"description": "Phone number field hint text"}, "nationalIdHint": "Optional identification number", "@nationalIdHint": {"description": "National ID field hint text"}, "notesHint": "Additional notes about the customer", "@notesHint": {"description": "Notes field hint text"}, "activeCustomer": "Active Customer", "@activeCustomer": {"description": "Active customer status label"}, "inactiveCustomer": "Inactive Customer", "@inactiveCustomer": {"description": "Inactive customer status label"}, "blockedCustomer": "Blocked Customer", "@blockedCustomer": {"description": "Blocked customer status label"}, "serviceTitle": "Service Title", "@serviceTitle": {"description": "Service title field label"}, "serviceTitleHint": "e.g., Consultation, Haircut, Massage", "@serviceTitleHint": {"description": "Service title field hint text"}, "serviceTitleRequired": "Service title is required", "@serviceTitleRequired": {"description": "Service title validation error"}, "titleMinLength": "Title must be at least 2 characters", "@titleMinLength": {"description": "Title minimum length validation error"}, "creditPointsRequired": "Credit Points Required", "@creditPointsRequired": {"description": "Credit points field label"}, "creditPointsHint": "Credits needed to book this service (minimum 1)", "@creditPointsHint": {"description": "Credit points field hint text"}, "creditPointsRequiredError": "Credit points requirement is required", "@creditPointsRequiredError": {"description": "Credit points validation error"}, "creditPointsPositive": "Credit points must be a positive number", "@creditPointsPositive": {"description": "Credit points positive number validation error"}, "deleteService": "Delete Service", "@deleteService": {"description": "Delete service dialog title"}, "deleteServiceConfirm": "Are you sure you want to delete \"{serviceName}\"?\n\nThis action cannot be undone.", "@deleteServiceConfirm": {"description": "Delete service confirmation message", "placeholders": {"serviceName": {"type": "String", "description": "Name of the service to delete"}}}, "activate": "Activate", "@activate": {"description": "Activate button text"}, "deactivate": "Deactivate", "@deactivate": {"description": "Deactivate button text"}, "minutes": "min", "@minutes": {"description": "Minutes abbreviation"}, "serviceDeletedSuccessfully": "Service \"{serviceName}\" deleted successfully", "@serviceDeletedSuccessfully": {"description": "Service deletion success message", "placeholders": {"serviceName": {"type": "String", "description": "Name of the deleted service"}}}, "failedToDeleteService": "Failed to delete service \"{serviceName}\"", "@failedToDeleteService": {"description": "Service deletion failure message", "placeholders": {"serviceName": {"type": "String", "description": "Name of the service that failed to delete"}}}, "priceNotSet": "Price not set", "@priceNotSet": {"description": "Text shown when service price is not set"}, "active": "Active", "@active": {"description": "Active checkbox title"}, "requiresCreditPoints": "Requires {points} credit points to book", "@requiresCreditPoints": {"description": "Credit points requirement message", "placeholders": {"points": {"type": "int", "description": "Number of credit points required"}}}, "hours": "h", "@hours": {"description": "Hours abbreviation for duration"}, "inactive": "Inactive", "@inactive": {"description": "Inactive status text"}, "queue": "Queue", "@queue": {"description": "Queue label (singular)"}, "queues": "Queues", "@queues": {"description": "Queues screen title"}, "queueCount": "{count} {count, plural, =1{queue} other{queues}}", "@queueCount": {"description": "Queue count with proper pluralization", "placeholders": {"count": {"type": "int", "description": "Number of queues"}}}, "errorLoadingQueues": "Error loading queues", "@errorLoadingQueues": {"description": "Error message when loading queues fails"}, "createLocationsFirstMessage": "Create locations first to manage queues.", "@createLocationsFirstMessage": {"description": "Message when no locations exist for queue management"}, "noQueuesFound": "No Queues Found", "@noQueuesFound": {"description": "No queues found title"}, "noQueuesForLocation": "No queues found for the selected location.", "@noQueuesForLocation": {"description": "No queues found for specific location message"}, "createFirstQueue": "Create your first queue to get started.", "@createFirstQueue": {"description": "Create first queue message"}, "addQueue": "Add <PERSON>", "@addQueue": {"description": "Add queue button text"}, "deleteQueue": "Delete Queue", "@deleteQueue": {"description": "Delete queue dialog title"}, "deleteQueueConfirmation": "Are you sure you want to delete the queue \"{queueName}\"?\\n\\nThis action cannot be undone.", "@deleteQueueConfirmation": {"description": "Delete queue confirmation message", "placeholders": {"queueName": {"type": "String", "description": "Name of the queue to delete"}}}, "customerCount": "{count} {count, plural, =1{customer} other{customers}}", "@customerCount": {"description": "Customer count with proper pluralization", "placeholders": {"count": {"type": "int", "description": "Number of customers"}}}, "clearFilters": "Clear filters", "@clearFilters": {"description": "Clear filters button text"}, "manageCustomerProfiles": "Manage customer profiles", "@manageCustomerProfiles": {"description": "Customer feature: manage profiles"}, "trackCustomerHistory": "Track customer history", "@trackCustomerHistory": {"description": "Customer feature: track history"}, "sendNotifications": "Send notifications", "@sendNotifications": {"description": "Customer feature: send notifications"}, "customerPreferences": "Customer preferences", "@customerPreferences": {"description": "Customer feature: preferences"}, "loyaltyPrograms": "Loyalty programs", "@loyaltyPrograms": {"description": "Customer feature: loyalty programs"}, "call": "Call", "@call": {"description": "Call action button"}, "sms": "SMS", "@sms": {"description": "SMS action button"}, "book": "Book", "@book": {"description": "Book appointment action button"}, "editCustomer": "Edit Customer", "@editCustomer": {"description": "Edit customer screen title"}, "status": "Status", "@status": {"description": "Status label"}, "wilaya": "<PERSON><PERSON><PERSON>", "@wilaya": {"description": "Wilaya filter label"}, "dateAdded": "Date Added", "@dateAdded": {"description": "Date added filter label"}, "minimumAppointments": "Minimum Appointments", "@minimumAppointments": {"description": "Minimum appointments filter label"}, "minimumSpent": "Minimum Spent (DA)", "@minimumSpent": {"description": "Minimum spent filter label"}, "selectWilaya": "Select wilaya", "@selectWilaya": {"description": "Select wilaya dropdown hint"}, "applyFilters": "Apply Filters", "@applyFilters": {"description": "Apply filters button text"}, "clearAll": "Clear All", "@clearAll": {"description": "Clear all services button text"}, "searchCustomersPlaceholder": "Search by name, email, or phone...", "@searchCustomersPlaceholder": {"description": "Search input placeholder text for customers"}, "newCustomer": "New Customer", "@newCustomer": {"description": "New customer button text"}, "additionalInformation": "Additional Information", "@additionalInformation": {"description": "Additional information section title"}, "additionalNotesHint": "Additional notes about the customer", "@additionalNotesHint": {"description": "Hint text for additional notes field"}, "customerAlreadyExists": "Customer Already Exists", "@customerAlreadyExists": {"description": "Dialog title when customer already exists"}, "customerExistsInArchive": "A customer with this information already exists in the archive.", "@customerExistsInArchive": {"description": "Message when customer exists in archive"}, "wouldYouLikeToRestore": "Would you like to restore the existing customer instead?", "@wouldYouLikeToRestore": {"description": "Question about restoring existing customer"}, "restoreCustomer": "Restore Customer", "@restoreCustomer": {"description": "Restore customer button text"}, "customerRestoredSuccessfully": "Customer {firstName} {lastName} restored successfully", "@customerRestoredSuccessfully": {"description": "Success message when customer is restored", "placeholders": {"firstName": {"type": "String", "description": "Customer first name"}, "lastName": {"type": "String", "description": "Customer last name"}}}, "failedToRestoreCustomer": "Failed to restore customer: {error}", "@failedToRestoreCustomer": {"description": "Error message when customer restore fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "customerDeletedSuccessfully": "Customer {firstName} {lastName} deleted successfully", "@customerDeletedSuccessfully": {"description": "Success message when customer is deleted", "placeholders": {"firstName": {"type": "String", "description": "Customer first name"}, "lastName": {"type": "String", "description": "Customer last name"}}}, "failedToDeleteCustomer": "Failed to delete customer: {error}", "@failedToDeleteCustomer": {"description": "Error message when customer deletion fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "customerUpdatedSuccessfully": "Customer {firstName} {lastName} updated successfully", "@customerUpdatedSuccessfully": {"description": "Success message when customer is updated", "placeholders": {"firstName": {"type": "String", "description": "Customer first name"}, "lastName": {"type": "String", "description": "Customer last name"}}}, "failedToUpdateCustomer": "Failed to update customer: {error}", "@failedToUpdateCustomer": {"description": "Error message when customer update fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "searchAppointmentsPlaceholder": "Search appointments...", "@searchAppointmentsPlaceholder": {"description": "Search input placeholder for appointments"}, "filters": "Filters", "@filters": {"description": "Filters section title"}, "all": "All", "@all": {"description": "All filter option"}, "filterAppointments": "Filter Appointments", "@filterAppointments": {"description": "Filter appointments screen title"}, "customizeAppointmentView": "Customize your appointment view with filters", "@customizeAppointmentView": {"description": "Filter appointments description"}, "queueFilter": "<PERSON><PERSON>", "@queueFilter": {"description": "Queue filter section title"}, "pending": "Pending", "@pending": {"description": "Pending status text"}, "scheduled": "Scheduled", "@scheduled": {"description": "Scheduled status text"}, "confirmed": "Confirmed", "@confirmed": {"description": "Confirmed status text"}, "inProgress": "In Progress", "@inProgress": {"description": "In progress status text"}, "completed": "Completed", "@completed": {"description": "Completed status text"}, "canceled": "Canceled", "@canceled": {"description": "Canceled status text"}, "noShow": "No Show", "@noShow": {"description": "No show status text"}, "rescheduled": "Rescheduled", "@rescheduled": {"description": "Rescheduled status text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm appointment action"}, "startService": "Start Service", "@startService": {"description": "Start service action"}, "complete": "Complete", "@complete": {"description": "Complete status"}, "editAppointment": "Edit Appointment", "@editAppointment": {"description": "Edit appointment screen title"}, "customerDidNotShowUp": "Customer did not show up for appointment", "@customerDidNotShowUp": {"description": "No show appointment message"}, "sessionCompletedSuccessfully": "Session completed successfully", "@sessionCompletedSuccessfully": {"description": "Completed appointment message"}, "dateRange": "Date Range", "@dateRange": {"description": "Date range filter label"}, "startDate": "Start Date", "@startDate": {"description": "Start date field label"}, "endDate": "End Date", "@endDate": {"description": "End date field label"}, "statusFilter": "Status Filter", "@statusFilter": {"description": "Status filter section title"}, "quickSelection": "Quick Selection", "@quickSelection": {"description": "Quick date selection title"}, "today": "Today", "@today": {"description": "Today date option"}, "thisWeek": "This Week", "@thisWeek": {"description": "This week date option"}, "thisMonth": "This Month", "@thisMonth": {"description": "This month date option"}, "customRange": "Custom Range", "@customRange": {"description": "Custom date range title"}, "selectStartDate": "Select Start Date", "@selectStartDate": {"description": "Start date field placeholder"}, "selectEndDate": "Select End Date", "@selectEndDate": {"description": "End date field placeholder"}, "clear": "Clear", "@clear": {"description": "Clear button text"}, "statusLabel": "Status", "@statusLabel": {"description": "Status label for filter summary"}, "dateRangeLabel": "Date Range", "@dateRangeLabel": {"description": "Date range label for filter summary"}, "queueLabel": "Queue", "@queueLabel": {"description": "Queue label for filter summary"}, "any": "Any", "@any": {"description": "Any option for date range"}, "customerInformation": "Customer Information", "@customerInformation": {"description": "Customer information section title"}, "serviceDetails": "Service Details", "@serviceDetails": {"description": "Service details section title"}, "scheduling": "Scheduling", "@scheduling": {"description": "Scheduling section title"}, "customerRequired": "Customer *", "@customerRequired": {"description": "Customer field label with required indicator"}, "serviceRequired": "Service *", "@serviceRequired": {"description": "Service field label with required indicator"}, "locationRequired": "Location *", "@locationRequired": {"description": "Location field label with required indicator"}, "queueRequired": "Queue *", "@queueRequired": {"description": "Queue field label with required indicator"}, "dateRequired": "Date *", "@dateRequired": {"description": "Date field label with required indicator"}, "timeRangeRequired": "Time Range *", "@timeRangeRequired": {"description": "Time range field label with required indicator"}, "selectService": "Select Service", "@selectService": {"description": "Service dropdown label"}, "selectLocation": "Select Location", "@selectLocation": {"description": "Location dropdown label"}, "selectQueue": "Select Queue", "@selectQueue": {"description": "Queue dropdown label"}, "selectLocationFirst": "Select a location first", "@selectLocationFirst": {"description": "Queue dropdown disabled placeholder"}, "appointmentDate": "Appointment Date", "@appointmentDate": {"description": "Appointment date field label"}, "startTime": "Start Time", "@startTime": {"description": "Start time label"}, "endTime": "End Time", "@endTime": {"description": "End time label"}, "notesOptional": "Notes (Optional)", "@notesOptional": {"description": "Notes field label"}, "addNotesPlaceholder": "Add any additional notes...", "@addNotesPlaceholder": {"description": "Notes field placeholder"}, "pleaseSelectCustomer": "Please select a customer", "@pleaseSelectCustomer": {"description": "Customer validation error message"}, "pleaseSelectService": "Please select a service", "@pleaseSelectService": {"description": "Service validation error message"}, "pleaseSelectLocation": "Please select a location", "@pleaseSelectLocation": {"description": "Location selection validation message"}, "pleaseSelectQueue": "Please select a queue", "@pleaseSelectQueue": {"description": "Queue validation error message"}, "startTimeBeforeEndTime": "Start time must be before end time", "@startTimeBeforeEndTime": {"description": "Time validation error message"}, "appointmentCreatedSuccessfully": "Appointment created successfully", "@appointmentCreatedSuccessfully": {"description": "Success message after creating appointment"}, "appointmentNumber": "Appointment #", "@appointmentNumber": {"description": "Appointment number prefix"}, "customerLabel": "Customer", "@customerLabel": {"description": "Customer label in appointment info"}, "serviceLabel": "Service", "@serviceLabel": {"description": "Service label in appointment info"}, "originalTime": "Original Time", "@originalTime": {"description": "Original time label in edit appointment"}, "selectStartTime": "Select start time", "@selectStartTime": {"description": "Start time field placeholder"}, "selectEndTime": "Select end time", "@selectEndTime": {"description": "End time field placeholder"}, "pleaseSelectLocationFirst": "Please select a location first", "@pleaseSelectLocationFirst": {"description": "Message when no location is selected for queue"}, "noQueuesAvailable": "No queues available for selected location", "@noQueuesAvailable": {"description": "Message when no queues are available"}, "errorLoadingLocations": "Error loading locations", "@errorLoadingLocations": {"description": "Error message for location loading failure"}, "appointmentNotesOptional": "Appointment notes (optional)", "@appointmentNotesOptional": {"description": "Notes field label in edit appointment"}, "duration": "Duration", "@duration": {"description": "Duration field label"}, "time": "Time", "@time": {"description": "Time section title"}, "statusScheduled": "Scheduled", "@statusScheduled": {"description": "Scheduled appointment status"}, "statusConfirmed": "Confirmed", "@statusConfirmed": {"description": "Confirmed appointment status"}, "statusCompleted": "Completed", "@statusCompleted": {"description": "Completed appointment status"}, "statusCanceled": "Canceled", "@statusCanceled": {"description": "Canceled appointment status"}, "statusNoShow": "No Show", "@statusNoShow": {"description": "No show appointment status"}, "cancelAppointment": "<PERSON>cel Appointment", "@cancelAppointment": {"description": "Cancel appointment dialog title"}, "cancelAppointmentConfirmation": "Are you sure you want to cancel this appointment?", "@cancelAppointmentConfirmation": {"description": "Cancel appointment confirmation message"}, "cancellationReason": "Cancellation reason (optional)", "@cancellationReason": {"description": "Cancellation reason field label"}, "cancellationReasonHint": "Enter reason for cancellation...", "@cancellationReasonHint": {"description": "Cancellation reason field hint"}, "keepAppointment": "Keep Appointment", "@keepAppointment": {"description": "Keep appointment button text"}, "appointmentCancelledSuccessfully": "Appointment cancelled successfully", "@appointmentCancelledSuccessfully": {"description": "Appointment cancellation success message"}, "failedToCancelAppointment": "Failed to cancel appointment", "@failedToCancelAppointment": {"description": "Appointment cancellation failure message"}, "calendarSettings": "Calendar Settings", "@calendarSettings": {"description": "Calendar settings screen title"}, "configureCalendarDisplayPreferences": "Configure calendar display preferences", "@configureCalendarDisplayPreferences": {"description": "Calendar settings description"}, "timeSlotInterval": "Time Slot Interval", "@timeSlotInterval": {"description": "Time slot interval setting label"}, "selectInterval": "Select interval", "@selectInterval": {"description": "Time slot interval dropdown hint"}, "timeSlotIntervalDescription": "This setting determines how many time slots each hour is divided into. For example, 5 minutes will create 12 slots per hour, while 15 minutes creates 4 slots per hour.", "@timeSlotIntervalDescription": {"description": "Time slot interval explanation text"}, "apply": "Apply", "@apply": {"description": "Apply button text"}, "statusAndOverview": "Status & Overview", "@statusAndOverview": {"description": "Status and overview section title"}, "serviceInformation": "Service Information", "@serviceInformation": {"description": "Service information section title"}, "primaryLocation": "Primary Location", "@primaryLocation": {"description": "Default location name"}, "cancelAppointmentConfirm": "<PERSON>cel Appointment", "@cancelAppointmentConfirm": {"description": "Cancel appointment dialog title"}, "cancelAppointmentMessage": "Are you sure you want to cancel the appointment with {customerName}?", "@cancelAppointmentMessage": {"description": "Cancel appointment confirmation message", "placeholders": {"customerName": {"type": "String", "description": "Customer name"}}}, "completeAppointmentTitle": "Complete Appointment", "@completeAppointmentTitle": {"description": "Complete appointment dialog title"}, "completeAppointmentMessage": "Mark the appointment with {customerName} as completed?", "@completeAppointmentMessage": {"description": "Complete appointment confirmation message", "placeholders": {"customerName": {"type": "String", "description": "Customer name"}}}, "appointmentConfirmedFor": "Appointment confirmed for {customerName}", "@appointmentConfirmedFor": {"description": "Appointment confirmation success message", "placeholders": {"customerName": {"type": "String", "description": "Customer name"}}}, "appointmentCanceledFor": "Appointment with {customerName} canceled", "@appointmentCanceledFor": {"description": "Appointment cancellation success message", "placeholders": {"customerName": {"type": "String", "description": "Customer name"}}}, "customer": "Customer", "@customer": {"description": "Customer label"}, "service": "Service", "@service": {"description": "Service label"}, "location": "Location", "@location": {"description": "Location label"}, "profileOverview": "Profile Overview", "@profileOverview": {"description": "Profile overview section title"}, "statistics": "Statistics", "@statistics": {"description": "Statistics section title"}, "rating": "Rating", "@rating": {"description": "Rating label"}, "reviews": "Reviews", "@reviews": {"description": "Reviews label"}, "setupStatus": "Setup Status", "@setupStatus": {"description": "Setup status label"}, "incomplete": "Incomplete", "@incomplete": {"description": "Incomplete status"}, "businessLogo": "Business Logo", "@businessLogo": {"description": "Business logo section title"}, "provider": "Provider", "@provider": {"description": "Default provider title"}, "verified": "Verified", "@verified": {"description": "Verified status badge"}, "changeLogo": "Change Logo", "@changeLogo": {"description": "Change logo button text"}, "uploadLogo": "Upload Logo", "@uploadLogo": {"description": "Upload logo button text"}, "supportedFormats": "Supported formats: PNG, JPG, JPEG • Max size: 5MB", "@supportedFormats": {"description": "Logo upload format information"}, "noLogoUploaded": "No logo uploaded", "@noLogoUploaded": {"description": "No logo placeholder text"}, "uploadBusinessLogo": "Upload Business Logo", "@uploadBusinessLogo": {"description": "Upload business logo dialog title"}, "errorLoadingProfile": "Error Loading Profile", "@errorLoadingProfile": {"description": "Error loading profile title"}, "profileAndBusinessImages": "Profile & Business Images", "@profileAndBusinessImages": {"description": "Profile and business images section title"}, "professionalTitle": "Professional Title", "@professionalTitle": {"description": "Professional title field label"}, "actions": "Actions", "@actions": {"description": "Actions section title"}, "profilePicture": "Profile Picture", "@profilePicture": {"description": "Profile picture label"}, "titleRequired": "Title is required", "@titleRequired": {"description": "Title field validation message"}, "phoneRequired": "Phone number is required", "@phoneRequired": {"description": "Phone number field validation message"}, "noCategorySelected": "No category selected", "@noCategorySelected": {"description": "No category selected placeholder"}, "categoryCannotBeChanged": "Category cannot be changed", "@categoryCannotBeChanged": {"description": "Category field disabled message"}, "saving": "Saving...", "@saving": {"description": "Saving progress text"}, "saveChanges": "Save Changes", "@saveChanges": {"description": "Save changes button text"}, "profileUpdatedSuccessfully": "Profile updated successfully", "@profileUpdatedSuccessfully": {"description": "Profile update success message"}, "failedToUpdateProfile": "Failed to update profile: {error}", "@failedToUpdateProfile": {"description": "Profile update error message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "profilePictureUpdatedSuccessfully": "Profile picture updated successfully!", "@profilePictureUpdatedSuccessfully": {"description": "Profile picture update success message"}, "profileCompletion": "Profile Completion", "@profileCompletion": {"description": "Profile completion widget title"}, "overallProgress": "Overall Progress", "@overallProgress": {"description": "Overall progress section title"}, "percentComplete": "{percentage}% Complete", "@percentComplete": {"description": "Percentage complete text", "placeholders": {"percentage": {"type": "int", "description": "Completion percentage"}}}, "details": "Details", "@details": {"description": "Details section title"}, "profilePictureItem": "Profile Picture", "@profilePictureItem": {"description": "Profile picture completion item"}, "providerInfo": "Provider Info", "@providerInfo": {"description": "Provider info completion item"}, "nextSteps": "Next Steps", "@nextSteps": {"description": "Next steps section title"}, "failedToLoadProfileCompletion": "Failed to load profile completion", "@failedToLoadProfileCompletion": {"description": "Error message for profile completion loading failure"}, "uploadProfessionalProfilePicture": "Upload a professional profile picture", "@uploadProfessionalProfilePicture": {"description": "Next step: upload profile picture"}, "completeProviderInformation": "Complete provider information: business description", "@completeProviderInformation": {"description": "Next step: complete provider info"}, "addAtLeastOneServiceLocation": "Add at least one service location", "@addAtLeastOneServiceLocation": {"description": "Next step: add service location"}, "createAtLeastOneServiceOffering": "Create at least one service offering", "@createAtLeastOneServiceOffering": {"description": "Next step: create service offering"}, "setUpBookingQueuesTimeSlots": "Set up booking queues/time slots", "@setUpBookingQueuesTimeSlots": {"description": "Next step: set up booking queues"}, "customerProfile": "Customer Profile", "@customerProfile": {"description": "Customer profile screen title"}, "deleteCustomer": "Delete Customer", "@deleteCustomer": {"description": "Delete customer dialog title"}, "deleteCustomerConfirmation": "Are you sure you want to delete {firstName} {lastName}? This action cannot be undone.", "@deleteCustomerConfirmation": {"description": "Delete customer confirmation message", "placeholders": {"firstName": {"type": "String", "description": "Customer first name"}, "lastName": {"type": "String", "description": "Customer last name"}}}, "information": "Information", "@information": {"description": "Information section title"}, "mobile": "Mobile", "@mobile": {"description": "Mobile phone field label"}, "customerSince": "Customer Since", "@customerSince": {"description": "Customer since label"}, "customerStatistics": "Customer Statistics", "@customerStatistics": {"description": "Customer statistics section title"}, "totalSpent": "Total Spent", "@totalSpent": {"description": "Total spent label"}, "lastVisit": "Last Visit", "@lastVisit": {"description": "Last visit label"}, "never": "Never", "@never": {"description": "Never visited label"}, "recentAppointments": "Recent Appointments", "@recentAppointments": {"description": "Recent appointments section title"}, "noRecentAppointments": "No recent appointments", "@noRecentAppointments": {"description": "No recent appointments message"}, "addAppointmentFeatureComingSoon": "Add appointment feature coming soon", "@addAppointmentFeatureComingSoon": {"description": "Add appointment coming soon message"}, "loadingDashboard": "Loading dashboard...", "@loadingDashboard": {"description": "Loading dashboard message"}, "failedToLoadDashboard": "Failed to load dashboard", "@failedToLoadDashboard": {"description": "Failed to load dashboard error message"}, "unknownErrorOccurred": "Unknown error occurred", "@unknownErrorOccurred": {"description": "Unknown error message"}, "refreshingData": "Refreshing data...", "@refreshingData": {"description": "Refreshing data message"}, "failedToLoadActiveSessions": "Failed to load active sessions", "@failedToLoadActiveSessions": {"description": "Failed to load active sessions error message"}, "failedToLoadPendingAppointments": "Failed to load pending appointments", "@failedToLoadPendingAppointments": {"description": "Failed to load pending appointments error message"}, "processingEmergencyControl": "Processing emergency control...", "@processingEmergencyControl": {"description": "Processing emergency control message"}, "refreshService": "Refresh Service", "@refreshService": {"description": "Refresh service tooltip"}, "editService": "Edit Service", "@editService": {"description": "Edit service screen title"}, "locationName": "Location Name", "@locationName": {"description": "Location name field label"}, "locationNameHint": "e.g., Main Office, Branch Downtown", "@locationNameHint": {"description": "Location name field hint"}, "locationNameRequired": "Location name is required", "@locationNameRequired": {"description": "Location name validation error"}, "locationNameMinLength": "Location name must be at least 2 characters", "@locationNameMinLength": {"description": "Location name minimum length validation error"}, "locationNameMaxLength": "Location name must be less than 100 characters", "@locationNameMaxLength": {"description": "Location name maximum length validation error"}, "streetAddress": "Street Address", "@streetAddress": {"description": "Street address field label"}, "streetAddressHint": "e.g., 123 Main Street", "@streetAddressHint": {"description": "Street address field hint text"}, "streetAddressRequired": "Street address is required", "@streetAddressRequired": {"description": "Street address validation error"}, "pleaseEnterCompleteAddress": "Please enter a complete address", "@pleaseEnterCompleteAddress": {"description": "Address completeness validation error"}, "addressMaxLength": "Address must be less than 200 characters", "@addressMaxLength": {"description": "Address maximum length validation error"}, "country": "Country", "@country": {"description": "Country field label"}, "algeria": "Algeria", "@algeria": {"description": "Algeria country name"}, "monday": "Monday", "@monday": {"description": "Monday day name"}, "tuesday": "Tuesday", "@tuesday": {"description": "Tuesday day name"}, "wednesday": "Wednesday", "@wednesday": {"description": "Wednesday day name"}, "thursday": "Thursday", "@thursday": {"description": "Thursday day name"}, "friday": "Friday", "@friday": {"description": "Friday day name"}, "saturday": "Saturday", "@saturday": {"description": "Saturday day name"}, "sunday": "Sunday", "@sunday": {"description": "Sunday day name"}, "activeServiceSessions": "Active Service Sessions", "@activeServiceSessions": {"description": "Active service sessions card title"}, "viewAll": "View All", "@viewAll": {"description": "View all button text"}, "noActiveSessions": "No Active Sessions", "@noActiveSessions": {"description": "No active sessions message"}, "noActiveSessionsDescription": "There are no active service sessions at the moment", "@noActiveSessionsDescription": {"description": "No active sessions description"}, "pendingAppointments": "Pending Appointments", "@pendingAppointments": {"description": "Pending appointments card title"}, "allCaughtUp": "All Caught Up!", "@allCaughtUp": {"description": "All caught up message"}, "noPendingAppointments": "No pending appointments to review", "@noPendingAppointments": {"description": "No pending appointments message"}, "todaysSchedule": "Today's Schedule", "@todaysSchedule": {"description": "Today's schedule card title"}, "unableToRefreshData": "Unable to refresh data. Showing cached information.", "@unableToRefreshData": {"description": "Error message when unable to refresh data"}, "goodMorning": "Good morning", "@goodMorning": {"description": "Morning greeting"}, "goodAfternoon": "Good afternoon", "@goodAfternoon": {"description": "Afternoon greeting"}, "goodEvening": "Good evening", "@goodEvening": {"description": "Evening greeting"}, "waiting": "waiting", "@waiting": {"description": "Waiting status text"}, "moreQueues": "+{count} more queues", "@moreQueues": {"description": "More queues text", "placeholders": {"count": {"type": "int", "description": "Number of additional queues"}}}, "noActiveQueues": "No active queues", "@noActiveQueues": {"description": "No active queues message"}, "total": "Total", "@total": {"description": "Total label"}, "upcoming": "Upcoming", "@upcoming": {"description": "Upcoming label"}, "appointmentsAwaitingAction": "{count} appointment{plural} awaiting action", "@appointmentsAwaitingAction": {"description": "Appointments awaiting action message", "placeholders": {"count": {"type": "int", "description": "Number of appointments"}, "plural": {"type": "String", "description": "Plural suffix for appointments"}}}, "quickActions": "Quick Actions", "@quickActions": {"description": "Quick actions section title"}, "newService": "New Service", "@newService": {"description": "New service button text"}, "newQueue": "New Queue", "@newQueue": {"description": "New queue button text"}, "newAppointment": "New Appointment", "@newAppointment": {"description": "New appointment button text"}, "noUpcomingAppointments": "No upcoming appointments", "@noUpcomingAppointments": {"description": "No upcoming appointments message"}, "allAppointmentsCompleted": "All appointments for today are completed", "@allAppointmentsCompleted": {"description": "All appointments completed message"}, "todaysSummary": "Today's Summary", "@todaysSummary": {"description": "Today's summary section title"}, "updatedSecondsAgo": "Updated {seconds}s ago", "@updatedSecondsAgo": {"description": "Updated seconds ago message", "placeholders": {"seconds": {"type": "int", "description": "Number of seconds"}}}, "updatedMinutesAgo": "Updated {minutes}m ago", "@updatedMinutesAgo": {"description": "Updated minutes ago message", "placeholders": {"minutes": {"type": "int", "description": "Number of minutes"}}}, "updatedHoursAgo": "Updated {hours}h ago", "@updatedHoursAgo": {"description": "Updated hours ago message", "placeholders": {"hours": {"type": "int", "description": "Number of hours"}}}, "updatedDaysAgo": "Updated {days}d ago", "@updatedDaysAgo": {"description": "Updated days ago message", "placeholders": {"days": {"type": "int", "description": "Number of days"}}}, "justNow": "Just now", "@justNow": {"description": "Just now time indicator"}, "dataRefresh": "Data Refresh", "@dataRefresh": {"description": "Data refresh section title"}, "lastUpdated": "Last updated: {timeAgo}", "@lastUpdated": {"description": "Last updated message", "placeholders": {"timeAgo": {"type": "String", "description": "Time ago string"}}}, "byLocation": "By Location", "@byLocation": {"description": "By location tab label"}, "allQueues": "All Queues", "@allQueues": {"description": "All queues tab label"}, "scheduleManagement": "Schedule Management", "@scheduleManagement": {"description": "Schedule management screen title"}, "weeklyView": "Weekly View", "@weeklyView": {"description": "Weekly view tab label"}, "listView": "List View", "@listView": {"description": "List view tab label"}, "appSettings": "App Settings", "@appSettings": {"description": "App settings section header"}, "account": "Account", "@account": {"description": "Account section header"}, "management": "Management", "@management": {"description": "Management section header"}, "manageServiceOfferings": "Manage your service offerings", "@manageServiceOfferings": {"description": "Services description text"}, "configureBusinessLocations": "Configure business locations", "@configureBusinessLocations": {"description": "Locations description text"}, "setupAppointmentQueues": "Set up appointment queues", "@setupAppointmentQueues": {"description": "Queues description text"}, "getHelpAndSupport": "Get help and support", "@getHelpAndSupport": {"description": "Help and support description text"}, "signOutOfAccount": "Sign out of your account", "@signOutOfAccount": {"description": "Logout description text"}, "queueManagement": "Queue Management", "@queueManagement": {"description": "Queue management screen title"}, "createService": "Create Service", "@createService": {"description": "Create service screen title"}, "createNewService": "Create New Service", "@createNewService": {"description": "Create service header title"}, "addNewServiceDescription": "Add a new service to your business offerings", "@addNewServiceDescription": {"description": "Create service header description"}, "serviceAvailableInfo": "Your service will be available for booking once created. You can edit or disable it later from the services list.", "@serviceAvailableInfo": {"description": "Service creation info message"}, "creatingService": "Creating Service...", "@creatingService": {"description": "Creating service loading text"}, "description": "Description", "@description": {"description": "Description field label"}, "describeYourService": "Describe your service", "@describeYourService": {"description": "Description field hint"}, "minutesShort": "m", "@minutesShort": {"description": "Very short minutes abbreviation for dropdowns"}, "hoursShort": "h", "@hoursShort": {"description": "Hours abbreviation"}, "createLocation": "Create Location", "@createLocation": {"description": "Create location screen title"}, "nameMinLength": "Name must be at least 2 characters", "@nameMinLength": {"description": "Name minimum length validation error"}, "nameMaxLength": "Name must be less than 100 characters", "@nameMaxLength": {"description": "Name maximum length validation error"}, "shortName": "Short Name", "@shortName": {"description": "Short name field label"}, "shortNameHint": "e.g., ABC Clinic, XYZ Salon", "@shortNameHint": {"description": "Short name field hint"}, "shortNameMaxLength": "Short name must be less than 100 characters", "@shortNameMaxLength": {"description": "Short name validation error"}, "city": "City", "@city": {"description": "City field label"}, "cityRequired": "City is required", "@cityRequired": {"description": "City validation error"}, "selectValidCity": "Please select a valid Algerian city", "@selectValidCity": {"description": "City validation error for invalid selection"}, "selectAlgerianCity": "Select an Algerian city", "@selectAlgerianCity": {"description": "City dropdown placeholder text"}, "countryRequired": "Country is required", "@countryRequired": {"description": "Country validation error"}, "onlyAvailableInAlgeria": "Currently only available in Algeria", "@onlyAvailableInAlgeria": {"description": "Country validation error for non-Algeria"}, "locationCreatedSuccessfully": "Location \"{locationName}\" created successfully", "@locationCreatedSuccessfully": {"description": "Location creation success message", "placeholders": {"locationName": {"type": "String", "description": "Name of the created location"}}}, "failedToCreateLocation": "Failed to create location", "@failedToCreateLocation": {"description": "Location creation failure message"}, "locationUpdatedSuccessfully": "Location \"{locationName}\" updated successfully", "@locationUpdatedSuccessfully": {"description": "Location update success message", "placeholders": {"locationName": {"type": "String", "description": "Name of the updated location"}}}, "failedToUpdateLocation": "Failed to update location", "@failedToUpdateLocation": {"description": "Location update failure message"}, "createNewLocation": "Create New Location", "@createNewLocation": {"description": "Create new location header title"}, "addNewBusinessLocation": "Add a new business location with address and operating hours", "@addNewBusinessLocation": {"description": "Create new location header description"}, "locationInformation": "Location Information", "@locationInformation": {"description": "Location information section header"}, "address": "Address", "@address": {"description": "Address field label"}, "addressHint": "e.g., 123 Main Street, Building A", "@addressHint": {"description": "Address field hint"}, "addressRequired": "Address is required", "@addressRequired": {"description": "Address validation error"}, "postalCode": "Postal Code", "@postalCode": {"description": "Postal code field label"}, "postalCodeHint": "e.g., 16000 (optional)", "@postalCodeHint": {"description": "Postal code field hint"}, "timezone": "Timezone", "@timezone": {"description": "Timezone field label"}, "timezoneRequired": "Timezone is required", "@timezoneRequired": {"description": "Timezone validation error"}, "locationCoordinates": "Location Coordinates", "@locationCoordinates": {"description": "Location coordinates section header"}, "getCurrentLocationDescription": "Get your current location to help customers find you easily.", "@getCurrentLocationDescription": {"description": "Location coordinates description"}, "latitude": "Latitude", "@latitude": {"description": "Latitude field label"}, "longitude": "Longitude", "@longitude": {"description": "Longitude field label"}, "willBeFilledAutomatically": "Will be filled automatically", "@willBeFilledAutomatically": {"description": "Coordinates field hint"}, "pleaseGetCurrentLocation": "Please get current location", "@pleaseGetCurrentLocation": {"description": "Coordinates validation error"}, "invalidLatitude": "Invalid latitude", "@invalidLatitude": {"description": "Latitude validation error"}, "invalidLongitude": "Invalid longitude", "@invalidLongitude": {"description": "Longitude validation error"}, "latitudeMustBeBetween": "Latitude must be between -90 and 90", "@latitudeMustBeBetween": {"description": "Latitude range validation error"}, "longitudeMustBeBetween": "Longitude must be between -180 and 180", "@longitudeMustBeBetween": {"description": "Longitude range validation error"}, "getCurrentLocation": "Get Current Location", "@getCurrentLocation": {"description": "Get current location button text"}, "amenities": "Amenities", "@amenities": {"description": "Amenities section header"}, "parkingAvailable": "Parking Available", "@parkingAvailable": {"description": "Parking amenity label"}, "onsiteParkingForCustomers": "On-site parking for customers", "@onsiteParkingForCustomers": {"description": "Parking amenity description"}, "elevatorAccess": "Elevator Access", "@elevatorAccess": {"description": "Elevator amenity label"}, "buildingHasElevatorAccess": "Building has elevator access", "@buildingHasElevatorAccess": {"description": "Elevator amenity description"}, "wheelchairAccessible": "Wheelchair Accessible", "@wheelchairAccessible": {"description": "Wheelchair accessibility amenity label"}, "accessibleForPeopleWithDisabilities": "Accessible for people with disabilities", "@accessibleForPeopleWithDisabilities": {"description": "Wheelchair accessibility amenity description"}, "openingHours": "Opening Hours", "@openingHours": {"description": "Opening hours section header"}, "closed": "Closed", "@closed": {"description": "Day status when closed"}, "open": "Open", "@open": {"description": "Day status when open"}, "tipToggleSwitchDayOpenClosed": "Tip: Toggle the switch to mark a day as open or closed", "@tipToggleSwitchDayOpenClosed": {"description": "Opening hours tip text"}, "locationWillBeAddedToBusinessLocations": "This location will be added to your business locations. You can manage all locations from the locations section", "@locationWillBeAddedToBusinessLocations": {"description": "Location creation info card text"}, "locationAvailableForQueueManagement": "Your location will be available for queue and appointment management once created. Make sure to set accurate coordinates and operating hours", "@locationAvailableForQueueManagement": {"description": "Location creation guidance text"}, "creatingLocation": "Creating Location...", "@creatingLocation": {"description": "Creating location loading text"}, "editLocation": "Edit Location", "@editLocation": {"description": "Edit location screen title"}, "updateLocationDetails": "Update location details, address, and operating hours", "@updateLocationDetails": {"description": "Edit location header description"}, "updateCoordinatesDescription": "Update coordinates if the location has moved to help customers find you easily", "@updateCoordinatesDescription": {"description": "Location coordinates update description"}, "changesWillBeUpdatedAcrossServices": "Changes to this location will be updated across all your business services and queues", "@changesWillBeUpdatedAcrossServices": {"description": "Edit location info card text about service updates"}, "changesWillBeSavedImmediately": "Changes will be saved immediately and reflected in your location settings. Existing queues and appointments will not be affected", "@changesWillBeSavedImmediately": {"description": "Edit location info card text about immediate saving"}, "savingChanges": "Saving Changes...", "@savingChanges": {"description": "Saving changes loading text"}, "updateCurrentLocation": "Update Current Location", "@updateCurrentLocation": {"description": "Update current location button text"}, "fax": "Fax", "@fax": {"description": "Fax field label"}, "floor": "Floor", "@floor": {"description": "Floor field label"}, "mobileHint": "e.g., +213 555-123456", "@mobileHint": {"description": "Mobile phone field hint text"}, "faxHint": "e.g., +213 555-123456", "@faxHint": {"description": "Fax field hint text"}, "floorHint": "e.g., 5th Floor", "@floorHint": {"description": "Floor field hint text"}, "createQueue": "Create Queue", "@createQueue": {"description": "Create queue header title"}, "editQueue": "<PERSON>", "@editQueue": {"description": "Edit queue header title"}, "queueInformation": "Queue Information", "@queueInformation": {"description": "Queue information section header"}, "queueName": "Queue Name", "@queueName": {"description": "Queue name field label"}, "queueNameHint": "e.g., General <PERSON>, VIP Queue, Walk-ins", "@queueNameHint": {"description": "Queue name field hint text"}, "queueServices": "Queue Services", "@queueServices": {"description": "Queue services section header"}, "selectServicesDescription": "Select services that will be available in this queue", "@selectServicesDescription": {"description": "Queue services selection description"}, "selectAll": "Select All", "@selectAll": {"description": "Select all services button text"}, "servicesSelected": "{count} of {total} selected", "@servicesSelected": {"description": "Services selection counter text", "placeholders": {"count": {"type": "int"}, "total": {"type": "int"}}}, "queueNameRequired": "Queue name is required", "@queueNameRequired": {"description": "Queue name required validation message"}, "queueNameMinLength": "Name must be at least 2 characters", "@queueNameMinLength": {"description": "Queue name minimum length validation message"}, "queueNameMaxLength": "Queue name cannot exceed 100 characters", "@queueNameMaxLength": {"description": "Queue name maximum length validation message"}, "loadingServices": "Loading services...", "@loadingServices": {"description": "Loading services text"}, "credits": "credits", "@credits": {"description": "Credits text label"}, "pleaseSelectAtLeastOneService": "Please select at least one service", "@pleaseSelectAtLeastOneService": {"description": "Service selection validation message"}, "queueOperatingHours": "Queue Operating Hours", "@queueOperatingHours": {"description": "Queue operating hours section header"}, "setQueueAvailabilityDescription": "Set when this queue is available for appointments", "@setQueueAvailabilityDescription": {"description": "Queue operating hours description"}, "noServicesAvailable": "No services available", "@noServicesAvailable": {"description": "No services available message"}, "createServicesFirstMessage": "Create services first to assign them to queues.", "@createServicesFirstMessage": {"description": "Create services first instruction message"}, "addServices": "Add Services", "@addServices": {"description": "Add services button text"}, "serviceDeliveryType": "Service Delivery Type", "@serviceDeliveryType": {"description": "Service delivery type field label"}, "atBusinessLocation": "At Business Location", "@atBusinessLocation": {"description": "Delivery type option - at business"}, "atCustomerLocation": "At Customer Location", "@atCustomerLocation": {"description": "Delivery type option - at customer"}, "bothOptions": "Both Options", "@bothOptions": {"description": "Delivery type option - both locations"}, "selectDeliveryType": "Please select a delivery type", "@selectDeliveryType": {"description": "Delivery type validation error"}, "selectRegions": "Select regions...", "@selectRegions": {"description": "Regions selection placeholder"}, "regionsSelected": "{count} region(s) selected", "@regionsSelected": {"description": "Regions selection count", "placeholders": {"count": {"type": "int", "description": "Number of selected regions"}}}, "customColor": "Custom Color", "@customColor": {"description": "Custom color input field label"}, "invalidHexColor": "Invalid hex color", "@invalidHexColor": {"description": "Hex color validation error"}, "editServiceTitle": "Edit {serviceName}", "@editServiceTitle": {"description": "Edit service screen title with service name", "placeholders": {"serviceName": {"type": "String", "description": "Name of the service being edited"}}}, "updateServiceDescription": "Update \"{serviceName}\" details and settings", "@updateServiceDescription": {"description": "Edit service header description", "placeholders": {"serviceName": {"type": "String", "description": "Name of the service being edited"}}}, "changesWillBeSaved": "Changes will be saved immediately and reflected in your service offerings. Existing appointments will not be affected.", "@changesWillBeSaved": {"description": "Edit service info message"}, "errorLoadingService": "Error loading service: {error}", "@errorLoadingService": {"description": "Error loading service message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "errorRefreshingService": "Error refreshing service: {error}", "@errorRefreshingService": {"description": "Error refreshing service message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "serviceUpdatedSuccessfully": "Service \"{serviceName}\" updated successfully", "@serviceUpdatedSuccessfully": {"description": "Service update success message", "placeholders": {"serviceName": {"type": "String", "description": "Name of the updated service"}}}, "failedToUpdateService": "Failed to update service", "@failedToUpdateService": {"description": "Service update failure message"}, "errorUpdatingService": "Error updating service: {error}", "@errorUpdatingService": {"description": "Error updating service message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "price": "Price (DA)", "@price": {"description": "Price field label"}, "priceRequired": "Price is required", "@priceRequired": {"description": "Price validation error"}, "enterValidPrice": "Enter valid price", "@enterValidPrice": {"description": "Price validation error"}, "serviceDelivery": "Service Delivery", "@serviceDelivery": {"description": "Service delivery section title"}, "whereProvideService": "Where do you provide this service?", "@whereProvideService": {"description": "Service delivery subtitle"}, "appearance": "Appearance", "@appearance": {"description": "Appearance section title"}, "serviceOptions": "Service Options", "@serviceOptions": {"description": "Service options section title"}, "publicService": "Public Service", "@publicService": {"description": "Public service checkbox title"}, "visibleToAllCustomers": "Visible to all customers", "@visibleToAllCustomers": {"description": "Public service checkbox subtitle"}, "acceptOnlineBookings": "Accept Online Bookings", "@acceptOnlineBookings": {"description": "Online bookings checkbox title"}, "allowCustomersBookOnline": "Allow customers to book online", "@allowCustomersBookOnline": {"description": "Online bookings checkbox subtitle"}, "acceptNewCustomers": "Accept New Customers", "@acceptNewCustomers": {"description": "New customers checkbox title"}, "allowNewCustomersBook": "Allow new customers to book", "@allowNewCustomersBook": {"description": "New customers checkbox subtitle"}, "enableNotifications": "Enable Notifications", "@enableNotifications": {"description": "Notifications checkbox title"}, "getNotifiedNewBookings": "Get notified for new bookings", "@getNotifiedNewBookings": {"description": "Notifications checkbox subtitle"}, "serviceAvailableForBooking": "Service is available for booking", "@serviceAvailableForBooking": {"description": "Active checkbox subtitle"}, "selectServedRegionsError": "Please select served regions for customer location services", "@selectServedRegionsError": {"description": "Served regions validation error"}, "themeModeLight": "Light", "@themeModeLight": {"description": "Light theme mode label"}, "themeModeDark": "Dark", "@themeModeDark": {"description": "Dark theme mode label"}, "themeModeSystem": "System", "@themeModeSystem": {"description": "System theme mode label"}, "searchServices": "Search services...", "@searchServices": {"description": "Search services placeholder text"}, "tryAdjustingSearchTerms": "Try adjusting your search terms", "@tryAdjustingSearchTerms": {"description": "No search results message"}, "noServicesFound": "No services found", "@noServicesFound": {"description": "No services found message"}, "errorLoadingServices": "Error loading services", "@errorLoadingServices": {"description": "Error loading services message"}, "noLocationsFound": "No locations found", "@noLocationsFound": {"description": "No locations found message"}, "failedToLoadLocations": "Failed to load locations", "@failedToLoadLocations": {"description": "Failed to load locations error message"}, "noLocationsAvailable": "No Locations Available", "@noLocationsAvailable": {"description": "No locations available message"}, "noCustomersFound": "No customers found", "@noCustomersFound": {"description": "No customers found message"}, "failedToLoadCustomers": "Failed to load customers", "@failedToLoadCustomers": {"description": "Failed to load customers error message"}, "addFirstCustomerToGetStarted": "Add your first customer to get started", "@addFirstCustomerToGetStarted": {"description": "Add first customer message"}, "addCustomer": "Add Customer", "@addCustomer": {"description": "Add customer button text"}, "noAppointmentsFound": "No appointments found", "@noAppointmentsFound": {"description": "No appointments found message"}, "tryAdjustingYourFilters": "Try adjusting your filters", "@tryAdjustingYourFilters": {"description": "Try adjusting filters message"}, "createFirstAppointmentToGetStarted": "Create your first appointment to get started", "@createFirstAppointmentToGetStarted": {"description": "Create first appointment message"}, "addAppointment": "Add Appointment", "@addAppointment": {"description": "Add appointment button text"}, "noAppointmentsScheduled": "No appointments scheduled", "@noAppointmentsScheduled": {"description": "No appointments scheduled message"}, "tapPlusButtonToAddAppointment": "Tap the + button to add an appointment", "@tapPlusButtonToAddAppointment": {"description": "Instruction to add appointment"}, "tapToChange": "Tap to change", "@tapToChange": {"description": "Tap to change instruction"}, "emailOrPhone": "Email or Phone", "@emailOrPhone": {"description": "Email or phone field label"}, "enterEmailOrPhone": "Enter your email or phone number", "@enterEmailOrPhone": {"description": "Email or phone field hint"}, "pleaseEnterEmailOrPhone": "Please enter your email or phone", "@pleaseEnterEmailOrPhone": {"description": "Email or phone validation error"}, "pleaseEnterPassword": "Please enter your password", "@pleaseEnterPassword": {"description": "Password validation error"}, "joinDaltiProvider": "<PERSON><PERSON>", "@joinDaltiProvider": {"description": "Join <PERSON> Provider title"}, "createBusinessAccount": "Create your business account", "@createBusinessAccount": {"description": "Create business account subtitle"}, "selectBusinessCategory": "Select your business category", "@selectBusinessCategory": {"description": "Business category dropdown hint"}, "createAccount": "Create Account", "@createAccount": {"description": "Create account button text"}, "pleaseEnterBusinessName": "Please enter your business name", "@pleaseEnterBusinessName": {"description": "Business name validation error"}, "businessCategory": "Business Category", "@businessCategory": {"description": "Business category field label"}, "pleaseEnterEmail": "Please enter your email", "@pleaseEnterEmail": {"description": "Email validation error"}, "pleaseEnterPhoneNumber": "Please enter your phone number", "@pleaseEnterPhoneNumber": {"description": "Phone number validation error"}, "pleaseConfirmPassword": "Please confirm your password", "@pleaseConfirmPassword": {"description": "Confirm password validation error"}, "resetPasswordDescription": "Enter your email address and we'll send you a verification code to reset your password", "@resetPasswordDescription": {"description": "Reset password description"}, "emailAddress": "Email Address", "@emailAddress": {"description": "Email address field label"}, "enterEmailAddress": "Enter your email address", "@enterEmailAddress": {"description": "Email address field hint"}, "pleaseEnterEmailAddress": "Please enter your email address", "@pleaseEnterEmailAddress": {"description": "Email address validation error"}, "pleaseEnterValidEmailAddress": "Please enter a valid email address", "@pleaseEnterValidEmailAddress": {"description": "Valid email address validation error"}, "sendResetCode": "Send Reset Code", "@sendResetCode": {"description": "Send reset code button text"}, "backToLogin": "Back to Login", "@backToLogin": {"description": "Back to login button text"}, "resetCodeSent": "Reset code sent!", "@resetCodeSent": {"description": "Reset code sent success message"}, "resetCodeSentDescription": "We sent a verification code to {email}", "@resetCodeSentDescription": {"description": "Reset code sent description with email", "placeholders": {"email": {"type": "String"}}}, "continueToVerification": "Continue to Verification", "@continueToVerification": {"description": "Continue to verification button text"}, "resendCode": "Resend code", "@resendCode": {"description": "Resend code button text"}, "resendCodeIn": "Resend code in {seconds}s", "@resendCodeIn": {"description": "Resend code countdown text", "placeholders": {"seconds": {"type": "int"}}}, "verifyResetCode": "Verify Reset Code", "@verifyResetCode": {"description": "Verify reset code title"}, "verifyResetCodeDescription": "We sent a 6-digit code to\n{email}", "@verifyResetCodeDescription": {"description": "Verify reset code description with email", "placeholders": {"email": {"type": "String"}}}, "enterVerificationCode": "Enter Verification Code", "@enterVerificationCode": {"description": "Enter verification code title"}, "codeExpiresIn": "Code expires in {minutes}:{seconds}", "@codeExpiresIn": {"description": "Code expiry countdown", "placeholders": {"minutes": {"type": "String"}, "seconds": {"type": "String"}}}, "codeHasExpired": "Code has expired", "@codeHasExpired": {"description": "Code expired message"}, "verifyCode": "Verify Code", "@verifyCode": {"description": "Verify code button text"}, "resendCodeWhenExpired": "Resend code when expired", "@resendCodeWhenExpired": {"description": "Resend code when expired button text"}, "backToEmailEntry": "Back to Email Entry", "@backToEmailEntry": {"description": "Back to email entry button text"}, "createNewPassword": "Create New Password", "@createNewPassword": {"description": "Create new password title"}, "createNewPasswordDescription": "Please create a strong password for your account", "@createNewPasswordDescription": {"description": "Create new password description"}, "resetTokenExpiresIn": "Reset token expires in {minutes} minutes", "@resetTokenExpiresIn": {"description": "Reset token expiry message", "placeholders": {"minutes": {"type": "int"}}}, "resetTokenExpired": "Reset token has expired. Please request a new password reset.", "@resetTokenExpired": {"description": "Reset token expired message"}, "enterNewPassword": "Enter your new password", "@enterNewPassword": {"description": "New password field hint"}, "confirmNewPasswordHint": "Confirm your new password", "@confirmNewPasswordHint": {"description": "Confirm new password field hint"}, "passwordRequirements": "Password Requirements", "@passwordRequirements": {"description": "Password requirements section title"}, "atLeast8Characters": "At least 8 characters", "@atLeast8Characters": {"description": "Password requirement: minimum length"}, "containsLowercaseLetter": "Contains lowercase letter", "@containsLowercaseLetter": {"description": "Password requirement: lowercase letter"}, "containsUppercaseLetter": "Contains uppercase letter", "@containsUppercaseLetter": {"description": "Password requirement: uppercase letter"}, "containsNumber": "Contains number", "@containsNumber": {"description": "Password requirement: number"}, "containsSpecialCharacter": "Contains special character", "@containsSpecialCharacter": {"description": "Password requirement: special character"}, "resetPasswordButton": "Reset Password", "@resetPasswordButton": {"description": "Reset password button text"}, "passwordDoesNotMeetRequirements": "Password does not meet requirements", "@passwordDoesNotMeetRequirements": {"description": "Password validation error for requirements"}, "createFirstService": "Create your first service to get started", "@createFirstService": {"description": "Empty services list message"}, "addService": "Add Service", "@addService": {"description": "Add service button text"}, "searchLocations": "Search locations...", "@searchLocations": {"description": "Search locations placeholder text"}, "createFirstLocation": "Create your first location to get started", "@createFirstLocation": {"description": "Empty locations list message"}, "addLocation": "Add Location", "@addLocation": {"description": "Add location button text"}, "schedulingInformation": "Scheduling Information", "@schedulingInformation": {"description": "Title for scheduling information section"}, "date": "Date", "@date": {"description": "Date label"}, "unknown": "Unknown", "@unknown": {"description": "Unknown status text"}, "businessSetup": "Business Setup", "@businessSetup": {"description": "Business setup screen title"}, "skipSetupTooltip": "Skip setup", "@skipSetupTooltip": {"description": "Skip setup tooltip"}, "businessInformation": "Business Information", "@businessInformation": {"description": "Business information section title"}, "tellUsAboutYourBusiness": "Tell us about your business", "@tellUsAboutYourBusiness": {"description": "Business information section subtitle"}, "businessNameRequired": "Business name is required", "@businessNameRequired": {"description": "Business name validation error"}, "businessNameMinLength": "Business name must be at least 2 characters", "@businessNameMinLength": {"description": "Business name minimum length validation error"}, "enterYourBusinessName": "Enter your business name", "@enterYourBusinessName": {"description": "Business name field hint"}, "businessDescription": "Business Description", "@businessDescription": {"description": "Business description field label"}, "businessDescriptionRequired": "Business description is required", "@businessDescriptionRequired": {"description": "Business description validation error"}, "businessDescriptionMinLength": "Description must be at least 10 characters", "@businessDescriptionMinLength": {"description": "Business description minimum length validation error"}, "describeWhatYourBusinessDoes": "Describe what your business does", "@describeWhatYourBusinessDoes": {"description": "Business description field hint"}, "businessCategoryRequired": "Please select a business category", "@businessCategoryRequired": {"description": "Business category validation error"}, "shortNameOptional": "Short Name (Optional)", "@shortNameOptional": {"description": "Short name field label"}, "businessLogoOptional": "Business Logo (Optional)", "@businessLogoOptional": {"description": "Business logo section title"}, "uploadBusinessLogoDescription": "Upload your business logo to help customers recognize your brand", "@uploadBusinessLogoDescription": {"description": "Business logo section description"}, "clickToUploadLogo": "Click to upload logo", "@clickToUploadLogo": {"description": "Upload logo button text"}, "businessInfoDescription": "This information will be displayed to your customers and used to set up your business profile.", "@businessInfoDescription": {"description": "Business information description text"}}
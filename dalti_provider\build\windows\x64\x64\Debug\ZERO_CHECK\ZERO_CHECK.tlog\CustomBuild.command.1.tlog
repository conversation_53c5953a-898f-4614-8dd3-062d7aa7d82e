^D:\DALTI-APP-DEV\DALTI-PROVIDER-FLUTTER\DALTI_PROVIDER\BUILD\WINDOWS\X64\CMAKEFILES\2334ECF8D65A7B3FC1938F8F4CFCF644\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/windows -BD:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64/dalti_provider.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd

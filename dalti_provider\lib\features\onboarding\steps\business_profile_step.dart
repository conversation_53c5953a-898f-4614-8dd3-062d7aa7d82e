import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';
import '../controllers/wizard_controller.dart';
import '../widgets/wizard_page.dart';
import '../providers/onboarding_provider.dart';
import '../components/form_fields/phone_numbers_widget.dart';
import '../components/navigation/wizard_navigation_buttons.dart';
import '../providers/wizard_navigation_provider.dart';
import '../../auth/models/provider_category.dart';
import '../../auth/widgets/hierarchical_category_dropdown.dart';
import '../../auth/services/category_service.dart';
import '../../../core/providers/app_providers.dart';
import '../../auth/providers/auth_provider.dart';
import '../../auth/models/auth_models.dart';
import '../widgets/logo_upload_widget.dart';
import '../../../core/utils/phone_validator.dart';
import '../../../generated/l10n/app_localizations.dart';

/// Business profile setup step
class BusinessProfileStep extends ConsumerStatefulWidget {
  final OnboardingWizardController controller;

  const BusinessProfileStep({super.key, required this.controller});

  @override
  ConsumerState<BusinessProfileStep> createState() =>
      _BusinessProfileStepState();
}

class _BusinessProfileStepState extends ConsumerState<BusinessProfileStep> {
  final _formKey = GlobalKey<FormState>();
  final _businessNameController = TextEditingController();
  final _shortNameController = TextEditingController();
  final _descriptionController = TextEditingController();

  String? _logoUrl;
  String? _logoFileId;

  ProviderCategory? _selectedCategory;
  PhoneNumberData _phoneData = const PhoneNumberData();

  @override
  void initState() {
    super.initState();
    // Delay loading to ensure auth state is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingData();
    });
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _shortNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// Load existing business profile data
  void _loadExistingData() {
    // Early exit if widget is disposed or onboarding is completing
    if (!mounted) return;

    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    final onboardingState = ref.read(onboardingNotifierProvider);

    // Don't load data if completion is in progress
    if (onboardingNotifier.isCompletionInProgress) {
      print('[BusinessProfileStep] Completion in progress, skipping data load');
      return;
    }

    // Don't load data if onboarding is already completed to avoid conflicts
    if (onboardingState.data?.isCompleted == true) {
      print(
        '[BusinessProfileStep] Onboarding already completed, skipping data load',
      );
      return;
    }

    final profile = onboardingState.data?.businessProfile;

    if (profile != null) {
      // Load existing onboarding data
      _businessNameController.text = profile.businessName;
      _shortNameController.text = profile.shortName ?? '';
      _descriptionController.text = profile.description;
      _logoUrl = profile.logoUrl;
      // Load selected category from profile.categoryId
      _loadSelectedCategoryById(profile.categoryId);

      // Load phone data
      _phoneData = PhoneNumberData(
        mobile: profile.mobile,
        landline: profile.landline,
        fax: profile.fax,
      );

      // Mark step as completed if data exists and update navigation
      final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
      wizardNotifier.markStepCompleted(OnboardingStep.businessProfile, true);
      widget.controller.markStepCompleted(OnboardingStep.businessProfile, true);
    } else {
      // No existing onboarding data, pre-populate from user's provider data
      _loadUserProviderData();
    }
  }

  /// Load user's provider data to pre-populate form
  void _loadUserProviderData() {
    // Check if completion is in progress
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    if (onboardingNotifier.isCompletionInProgress) {
      print(
        '[BusinessProfileStep] Completion in progress, skipping user data load',
      );
      return;
    }

    final authState = ref.read(authNotifierProvider);
    final provider = authState.provider;

    if (provider != null) {
      // Pre-populate business name from provider title
      _businessNameController.text = provider.title;

      // Pre-populate phone from provider phone
      if (provider.phone != null) {
        _phoneData = PhoneNumberData(mobile: provider.phone);
      }

      // Pre-populate category from provider category or providerCategoryId
      int? categoryId;

      // Check for category.id first (login payload structure)
      if (provider.category != null) {
        categoryId = provider.category!.id;
        print('[BusinessProfileStep] Using category.id: $categoryId');
      }
      // Fallback to providerCategoryId (signup payload structure)
      else if (provider.providerCategoryId > 0) {
        categoryId = provider.providerCategoryId;
        print('[BusinessProfileStep] Using providerCategoryId: $categoryId');
      }

      if (categoryId != null) {
        _loadSelectedCategoryById(categoryId);
      } else {
        print('[BusinessProfileStep] No category ID found in provider data');
      }
    }
  }

  /// Load selected category from category ID (unified method)
  Future<void> _loadSelectedCategoryById(int categoryId) async {
    try {
      // Early exit if widget is disposed or completion in progress
      if (!mounted) return;

      final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
      if (onboardingNotifier.isCompletionInProgress) {
        print(
          '[BusinessProfileStep] Completion in progress, canceling category load',
        );
        return;
      }

      final categoryService = ref.read(categoryServiceProvider);
      final category = await categoryService.getCategoryById(categoryId);

      // Check again after async call
      if (!mounted || onboardingNotifier.isCompletionInProgress) {
        print(
          '[BusinessProfileStep] Completion started during category load, aborting',
        );
        return;
      }

      if (category != null && mounted) {
        setState(() {
          _selectedCategory = category;
        });

        print(
          '[BusinessProfileStep] Successfully loaded category: ${category.name}',
        );

        // Simple validation trigger without nested callbacks
        if (mounted) {
          _onFieldChanged('');
        }
      } else {
        print('[BusinessProfileStep] Category not found for ID: $categoryId');
      }
    } catch (e) {
      if (mounted) {
        print(
          '[BusinessProfileStep] Error loading category by ID $categoryId: $e',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    // Don't build the form if completion is in progress
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    if (onboardingNotifier.isCompletionInProgress) {
      print(
        '[BusinessProfileStep] Completion in progress, showing minimal widget',
      );
      return Container(
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Completing setup...',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colors.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return WizardFormPage(
      step: OnboardingStep.businessProfile,
      title: l10n.businessInformation,
      subtitle: l10n.tellUsAboutYourBusiness,
      formKey: _formKey,
      fields: [
        // Business name field
        TextFormField(
          controller: _businessNameController,
          decoration: InputDecoration(
            labelText: '${l10n.businessName} *',
            hintText: l10n.enterYourBusinessName,
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.business),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return l10n.businessNameRequired;
            }
            if (value.trim().length < 2) {
              return l10n.businessNameMinLength;
            }
            return null;
          },
          onChanged: _onFieldChanged,
          textCapitalization: TextCapitalization.words,
        ),

        // Business description field
        TextFormField(
          controller: _descriptionController,
          decoration: InputDecoration(
            labelText: '${l10n.businessDescription} *',
            hintText: l10n.describeWhatYourBusinessDoes,
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.description),
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return l10n.businessDescriptionRequired;
            }
            if (value.trim().length < 10) {
              return l10n.businessDescriptionMinLength;
            }
            return null;
          },
          onChanged: _onFieldChanged,
          textCapitalization: TextCapitalization.sentences,
        ),

        // Business category dropdown with hierarchical structure
        HierarchicalCategoryDropdown(
          selectedCategory: _selectedCategory,
          decoration: InputDecoration(
            labelText: '${l10n.businessCategory} *',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.category),
          ),
          onChanged: (category) {
            // Don't process changes if completion is in progress or widget is disposed
            final onboardingNotifier = ref.read(
              onboardingNotifierProvider.notifier,
            );
            if (onboardingNotifier.isCompletionInProgress || !mounted) {
              print(
                '[BusinessProfileStep] Skipping category change - completion in progress or disposed',
              );
              return;
            }

            setState(() {
              _selectedCategory = category;
            });
            _onFieldChanged('');
          },
          validator: (category) {
            if (category == null) {
              return l10n.businessCategoryRequired;
            }
            return null;
          },
        ),

        // Short name field (optional)
        TextFormField(
          controller: _shortNameController,
          decoration: InputDecoration(
            labelText: l10n.shortNameOptional,
            hintText: l10n.shortNameHint,
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.short_text),
          ),
          onChanged: _onFieldChanged,
          textCapitalization: TextCapitalization.words,
        ),

        // Logo upload widget
        LogoUploadWidget(
          initialLogoUrl: _logoUrl,
          onLogoChanged: _onLogoChanged,
          title: l10n.businessLogoOptional,
          subtitle: l10n.uploadBusinessLogoDescription,
        ),

        // Phone numbers section using reusable component
        PhoneNumbersWidget(
          initialData: _phoneData,
          onChanged: _onPhoneDataChanged,
          title: 'Contact Information',
          subtitle: '',
          isRequired: true,
        ),

        // Info card
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: context.colors.primaryContainer.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: context.colors.primary.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: context.colors.primary, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'This information will be displayed to your customers and used to set up your business profile.',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Handle field changes to validate and update state
  void _onFieldChanged(String value) {
    // Don't process changes if completion is in progress
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    if (onboardingNotifier.isCompletionInProgress) {
      print(
        '[BusinessProfileStep] Completion in progress, skipping field change',
      );
      return;
    }

    // Validate form and update controller state
    final isValid = _validateForm();
    final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
    wizardNotifier.markStepCompleted(OnboardingStep.businessProfile, isValid);
    widget.controller.markStepCompleted(
      OnboardingStep.businessProfile,
      isValid,
    );

    if (isValid) {
      _saveBusinessProfile();
    }
  }

  /// Handle phone data changes
  void _onPhoneDataChanged(PhoneNumberData phoneData) {
    // Don't process changes if completion is in progress or widget is disposed
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    if (onboardingNotifier.isCompletionInProgress || !mounted) {
      print(
        '[BusinessProfileStep] Skipping phone data change - completion in progress or disposed',
      );
      return;
    }

    setState(() {
      _phoneData = phoneData;
    });
    _onFieldChanged(''); // Trigger validation
  }

  /// Handle logo changes
  void _onLogoChanged(String? logoUrl, String? fileId) {
    // Don't process changes if completion is in progress or widget is disposed
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    if (onboardingNotifier.isCompletionInProgress || !mounted) {
      print(
        '[BusinessProfileStep] Skipping logo change - completion in progress or disposed',
      );
      return;
    }

    setState(() {
      _logoUrl = logoUrl;
      _logoFileId = fileId;
    });
    _onFieldChanged(''); // Trigger validation and save
  }

  /// Validate the form
  bool _validateForm() {
    return _businessNameController.text.trim().isNotEmpty &&
        _businessNameController.text.trim().length >= 2 &&
        _descriptionController.text.trim().isNotEmpty &&
        _descriptionController.text.trim().length >= 10 &&
        _selectedCategory != null &&
        _phoneData.hasValidPhoneNumber;
  }

  /// Save business profile data
  void _saveBusinessProfile() {
    if (!_validateForm()) return;

    // Don't save if completion is in progress
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    if (onboardingNotifier.isCompletionInProgress) {
      print('[BusinessProfileStep] Completion in progress, skipping save');
      return;
    }

    final profile = BusinessProfile(
      businessName: _businessNameController.text.trim(),
      description: _descriptionController.text.trim(),
      categoryId: _selectedCategory!.id,
      categoryName: _selectedCategory!.name,
      shortName:
          _shortNameController.text.trim().isNotEmpty
              ? _shortNameController.text.trim()
              : null,
      logoUrl: _logoUrl,

      mobile: _phoneData.mobile,
      landline: _phoneData.landline,
      fax: _phoneData.fax,
    );

    // Save to onboarding provider
    ref.read(onboardingNotifierProvider.notifier).saveBusinessProfile(profile);
  }
}

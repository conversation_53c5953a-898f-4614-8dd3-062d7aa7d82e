/// Generated file. Do not edit.
///
/// Original file: lib/l10n/app_en.arb
/// To regenerate, run: `flutter gen-l10n`
///
/// Localization for Dalti Provider App
/// Supports: English (en), French (fr), Arabic (ar)

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';
import 'app_localizations_fr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
    Locale('fr'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Dalti Provider'**
  String get appTitle;

  /// Language label
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// English language option
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get languageEnglish;

  /// French language option
  ///
  /// In en, this message translates to:
  /// **'Français'**
  String get languageFrench;

  /// Arabic language option
  ///
  /// In en, this message translates to:
  /// **'العربية'**
  String get languageArabic;

  /// Settings screen title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Profile screen title
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Dashboard screen title
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// Appointments label
  ///
  /// In en, this message translates to:
  /// **'Appointments'**
  String get appointments;

  /// Appointment details screen title
  ///
  /// In en, this message translates to:
  /// **'Appointment Details'**
  String get appointmentDetails;

  /// Calendar screen title
  ///
  /// In en, this message translates to:
  /// **'Calendar'**
  String get calendar;

  /// Messages screen title
  ///
  /// In en, this message translates to:
  /// **'Messages'**
  String get messages;

  /// Customers plural form
  ///
  /// In en, this message translates to:
  /// **'customers'**
  String get customers;

  /// Services screen title
  ///
  /// In en, this message translates to:
  /// **'Services'**
  String get services;

  /// Locations screen title
  ///
  /// In en, this message translates to:
  /// **'Locations'**
  String get locations;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// OK button text
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// Yes button text
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No button text
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Error message title
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Success message title
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Message shown when language is changed
  ///
  /// In en, this message translates to:
  /// **'Language changed successfully'**
  String get languageChanged;

  /// Error message when language change fails
  ///
  /// In en, this message translates to:
  /// **'Failed to change language. Please try again.'**
  String get languageChangeError;

  /// Title for language selection dialog/screen
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// Label for current language display
  ///
  /// In en, this message translates to:
  /// **'Current Language'**
  String get currentLanguage;

  /// Login button text
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// Logout button text
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Register button text
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get register;

  /// Email field label
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Password field label
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Confirm password field label
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// First name field label
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get firstName;

  /// Last name field label
  ///
  /// In en, this message translates to:
  /// **'Last Name'**
  String get lastName;

  /// Phone field label
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// Business name field label
  ///
  /// In en, this message translates to:
  /// **'Business Name'**
  String get businessName;

  /// Add button text
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Update button text
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// Create button text
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Filter button text
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// Refresh button text
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// Back button text
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// Next button text
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// Previous button text
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// Close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Done button text
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Message when no data is available
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get noData;

  /// Network error message
  ///
  /// In en, this message translates to:
  /// **'Network error. Please check your connection.'**
  String get networkError;

  /// Server error message
  ///
  /// In en, this message translates to:
  /// **'Server error. Please try again later.'**
  String get serverError;

  /// Unknown error message
  ///
  /// In en, this message translates to:
  /// **'An unknown error occurred.'**
  String get unknownError;

  /// Validation error message
  ///
  /// In en, this message translates to:
  /// **'Please check your input and try again.'**
  String get validationError;

  /// Required field validation message
  ///
  /// In en, this message translates to:
  /// **'This field is required'**
  String get required;

  /// Invalid email validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get invalidEmail;

  /// Invalid phone validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid phone number'**
  String get invalidPhone;

  /// Password length validation error
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordTooShort;

  /// Password mismatch validation error
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// Help and support menu item
  ///
  /// In en, this message translates to:
  /// **'Help & Support'**
  String get helpAndSupport;

  /// Theme menu item
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// Light theme option
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get themeLight;

  /// Dark theme option
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get themeDark;

  /// System theme option
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get themeSystem;

  /// Logout confirmation dialog title
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logoutConfirmTitle;

  /// Logout confirmation dialog message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to logout?'**
  String get logoutConfirmMessage;

  /// Edit profile tooltip and button text
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get editProfile;

  /// App name for default page titles
  ///
  /// In en, this message translates to:
  /// **'Dalti Provider'**
  String get daltiProvider;

  /// Getting location loading text
  ///
  /// In en, this message translates to:
  /// **'Getting Location...'**
  String get gettingLocation;

  /// Button text to use current location
  ///
  /// In en, this message translates to:
  /// **'Use Current Location'**
  String get useCurrentLocation;

  /// Text shown when a field value is not specified
  ///
  /// In en, this message translates to:
  /// **'Not specified'**
  String get notSpecified;

  /// Title field label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// Category field label
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// Profile information section title
  ///
  /// In en, this message translates to:
  /// **'Profile Information'**
  String get profileInformation;

  /// About me section title
  ///
  /// In en, this message translates to:
  /// **'About Me'**
  String get aboutMe;

  /// Password reset success dialog title
  ///
  /// In en, this message translates to:
  /// **'Password Reset Successful'**
  String get passwordResetSuccessful;

  /// Password reset success dialog message
  ///
  /// In en, this message translates to:
  /// **'Your password has been reset successfully. You can now log in with your new password.'**
  String get passwordResetSuccessMessage;

  /// Button text to go to login screen
  ///
  /// In en, this message translates to:
  /// **'Go to Login'**
  String get goToLogin;

  /// Error message when password reset fails
  ///
  /// In en, this message translates to:
  /// **'Failed to reset password. Please try again.'**
  String get failedToResetPassword;

  /// Forgot password link text
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get forgotPassword;

  /// Reset password title
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPassword;

  /// New password field label
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// Confirm new password field label
  ///
  /// In en, this message translates to:
  /// **'Confirm New Password'**
  String get confirmNewPassword;

  /// OTP field label
  ///
  /// In en, this message translates to:
  /// **'Enter OTP'**
  String get enterOtp;

  /// Verify OTP button text
  ///
  /// In en, this message translates to:
  /// **'Verify OTP'**
  String get verifyOtp;

  /// Resend OTP button text
  ///
  /// In en, this message translates to:
  /// **'Resend OTP'**
  String get resendOtp;

  /// Message when OTP is sent
  ///
  /// In en, this message translates to:
  /// **'OTP sent to your email'**
  String get otpSent;

  /// Error message for invalid OTP
  ///
  /// In en, this message translates to:
  /// **'Invalid OTP. Please try again.'**
  String get invalidOtp;

  /// Welcome message on login screen
  ///
  /// In en, this message translates to:
  /// **'Welcome Back'**
  String get welcomeBack;

  /// Subtitle on login screen
  ///
  /// In en, this message translates to:
  /// **'Sign in to continue'**
  String get signInToContinue;

  /// Text before register link
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dontHaveAccount;

  /// Already have account text
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get alreadyHaveAccount;

  /// Sign up button text
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUp;

  /// Sign in button text
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// Appointments list screen title
  ///
  /// In en, this message translates to:
  /// **'Appointments'**
  String get appointmentsList;

  /// Notifications screen title
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Message shown when login is successful
  ///
  /// In en, this message translates to:
  /// **'Login successful!'**
  String get loginSuccessful;

  /// Message shown when login fails
  ///
  /// In en, this message translates to:
  /// **'Login failed'**
  String get loginFailed;

  /// Welcome message on login screen
  ///
  /// In en, this message translates to:
  /// **'Welcome to Dalti Provider'**
  String get welcomeToDaltiProvider;

  /// Subtitle on login screen
  ///
  /// In en, this message translates to:
  /// **'Sign in to manage your business'**
  String get signInToManageBusiness;

  /// Get started button text
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get getStarted;

  /// Skip setup dialog title
  ///
  /// In en, this message translates to:
  /// **'Skip Setup?'**
  String get skipSetup;

  /// Skip setup dialog message
  ///
  /// In en, this message translates to:
  /// **'You can complete your business setup later from the dashboard. However, some features may be limited until setup is complete.'**
  String get skipSetupMessage;

  /// Continue setup button text
  ///
  /// In en, this message translates to:
  /// **'Continue Setup'**
  String get continueSetup;

  /// Skip for now button text
  ///
  /// In en, this message translates to:
  /// **'Skip for Now'**
  String get skipForNow;

  /// Start conversation button text
  ///
  /// In en, this message translates to:
  /// **'Start Conversation'**
  String get startConversation;

  /// Error message when conversation creation fails
  ///
  /// In en, this message translates to:
  /// **'Failed to create conversation'**
  String get failedToCreateConversation;

  /// Customer dropdown placeholder
  ///
  /// In en, this message translates to:
  /// **'Select a customer'**
  String get selectCustomer;

  /// Label for initial message field
  ///
  /// In en, this message translates to:
  /// **'Initial message (optional):'**
  String get initialMessage;

  /// Placeholder text for message input
  ///
  /// In en, this message translates to:
  /// **'Type your message here...'**
  String get typeMessageHere;

  /// Service color section title
  ///
  /// In en, this message translates to:
  /// **'Service Color'**
  String get serviceColor;

  /// Customer management screen title
  ///
  /// In en, this message translates to:
  /// **'Customer Management'**
  String get customerManagement;

  /// Coming soon placeholder text
  ///
  /// In en, this message translates to:
  /// **'Coming Soon'**
  String get comingSoon;

  /// Feature description intro text
  ///
  /// In en, this message translates to:
  /// **'This feature will allow you to:'**
  String get thisFeatureWillAllowYouTo;

  /// Basic information section title
  ///
  /// In en, this message translates to:
  /// **'Basic Information'**
  String get basicInformation;

  /// Contact information section title
  ///
  /// In en, this message translates to:
  /// **'Contact Information'**
  String get contactInformation;

  /// Phone number field label
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// National ID field label
  ///
  /// In en, this message translates to:
  /// **'National ID'**
  String get nationalId;

  /// Notes field label
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// First name validation error
  ///
  /// In en, this message translates to:
  /// **'First name is required'**
  String get firstNameRequired;

  /// Last name validation error
  ///
  /// In en, this message translates to:
  /// **'Last name is required'**
  String get lastNameRequired;

  /// Valid email validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get pleaseEnterValidEmail;

  /// Phone validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid phone number'**
  String get pleaseEnterValidPhone;

  /// Email field hint text
  ///
  /// In en, this message translates to:
  /// **'<EMAIL>'**
  String get customerEmailHint;

  /// Phone number field hint text
  ///
  /// In en, this message translates to:
  /// **'+213 123 456 789'**
  String get phoneNumberHint;

  /// National ID field hint text
  ///
  /// In en, this message translates to:
  /// **'Optional identification number'**
  String get nationalIdHint;

  /// Notes field hint text
  ///
  /// In en, this message translates to:
  /// **'Additional notes about the customer'**
  String get notesHint;

  /// Active customer status label
  ///
  /// In en, this message translates to:
  /// **'Active Customer'**
  String get activeCustomer;

  /// Inactive customer status label
  ///
  /// In en, this message translates to:
  /// **'Inactive Customer'**
  String get inactiveCustomer;

  /// Blocked customer status label
  ///
  /// In en, this message translates to:
  /// **'Blocked Customer'**
  String get blockedCustomer;

  /// Service title field label
  ///
  /// In en, this message translates to:
  /// **'Service Title'**
  String get serviceTitle;

  /// Service title field hint text
  ///
  /// In en, this message translates to:
  /// **'e.g., Consultation, Haircut, Massage'**
  String get serviceTitleHint;

  /// Service title validation error
  ///
  /// In en, this message translates to:
  /// **'Service title is required'**
  String get serviceTitleRequired;

  /// Title minimum length validation error
  ///
  /// In en, this message translates to:
  /// **'Title must be at least 2 characters'**
  String get titleMinLength;

  /// Credit points field label
  ///
  /// In en, this message translates to:
  /// **'Credit Points Required'**
  String get creditPointsRequired;

  /// Credit points field hint text
  ///
  /// In en, this message translates to:
  /// **'Credits needed to book this service (minimum 1)'**
  String get creditPointsHint;

  /// Credit points validation error
  ///
  /// In en, this message translates to:
  /// **'Credit points requirement is required'**
  String get creditPointsRequiredError;

  /// Credit points positive number validation error
  ///
  /// In en, this message translates to:
  /// **'Credit points must be a positive number'**
  String get creditPointsPositive;

  /// Delete service dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Service'**
  String get deleteService;

  /// Delete service confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete \"{serviceName}\"?\n\nThis action cannot be undone.'**
  String deleteServiceConfirm(String serviceName);

  /// Activate button text
  ///
  /// In en, this message translates to:
  /// **'Activate'**
  String get activate;

  /// Deactivate button text
  ///
  /// In en, this message translates to:
  /// **'Deactivate'**
  String get deactivate;

  /// Minutes abbreviation
  ///
  /// In en, this message translates to:
  /// **'min'**
  String get minutes;

  /// Service deletion success message
  ///
  /// In en, this message translates to:
  /// **'Service \"{serviceName}\" deleted successfully'**
  String serviceDeletedSuccessfully(String serviceName);

  /// Service deletion failure message
  ///
  /// In en, this message translates to:
  /// **'Failed to delete service \"{serviceName}\"'**
  String failedToDeleteService(String serviceName);

  /// Text shown when service price is not set
  ///
  /// In en, this message translates to:
  /// **'Price not set'**
  String get priceNotSet;

  /// Active checkbox title
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// Credit points requirement message
  ///
  /// In en, this message translates to:
  /// **'Requires {points} credit points to book'**
  String requiresCreditPoints(int points);

  /// Hours abbreviation for duration
  ///
  /// In en, this message translates to:
  /// **'h'**
  String get hours;

  /// Inactive status text
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// Queue label (singular)
  ///
  /// In en, this message translates to:
  /// **'Queue'**
  String get queue;

  /// Queues screen title
  ///
  /// In en, this message translates to:
  /// **'Queues'**
  String get queues;

  /// Queue count with proper pluralization
  ///
  /// In en, this message translates to:
  /// **'{count} {count, plural, =1{queue} other{queues}}'**
  String queueCount(int count);

  /// Error message when loading queues fails
  ///
  /// In en, this message translates to:
  /// **'Error loading queues'**
  String get errorLoadingQueues;

  /// Message when no locations exist for queue management
  ///
  /// In en, this message translates to:
  /// **'Create locations first to manage queues.'**
  String get createLocationsFirstMessage;

  /// No queues found title
  ///
  /// In en, this message translates to:
  /// **'No Queues Found'**
  String get noQueuesFound;

  /// No queues found for specific location message
  ///
  /// In en, this message translates to:
  /// **'No queues found for the selected location.'**
  String get noQueuesForLocation;

  /// Create first queue message
  ///
  /// In en, this message translates to:
  /// **'Create your first queue to get started.'**
  String get createFirstQueue;

  /// Add queue button text
  ///
  /// In en, this message translates to:
  /// **'Add Queue'**
  String get addQueue;

  /// Delete queue dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Queue'**
  String get deleteQueue;

  /// Delete queue confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete the queue \"{queueName}\"?\\n\\nThis action cannot be undone.'**
  String deleteQueueConfirmation(String queueName);

  /// Customer count with proper pluralization
  ///
  /// In en, this message translates to:
  /// **'{count} {count, plural, =1{customer} other{customers}}'**
  String customerCount(int count);

  /// Clear filters button text
  ///
  /// In en, this message translates to:
  /// **'Clear filters'**
  String get clearFilters;

  /// Customer feature: manage profiles
  ///
  /// In en, this message translates to:
  /// **'Manage customer profiles'**
  String get manageCustomerProfiles;

  /// Customer feature: track history
  ///
  /// In en, this message translates to:
  /// **'Track customer history'**
  String get trackCustomerHistory;

  /// Customer feature: send notifications
  ///
  /// In en, this message translates to:
  /// **'Send notifications'**
  String get sendNotifications;

  /// Customer feature: preferences
  ///
  /// In en, this message translates to:
  /// **'Customer preferences'**
  String get customerPreferences;

  /// Customer feature: loyalty programs
  ///
  /// In en, this message translates to:
  /// **'Loyalty programs'**
  String get loyaltyPrograms;

  /// Call action button
  ///
  /// In en, this message translates to:
  /// **'Call'**
  String get call;

  /// SMS action button
  ///
  /// In en, this message translates to:
  /// **'SMS'**
  String get sms;

  /// Book appointment action button
  ///
  /// In en, this message translates to:
  /// **'Book'**
  String get book;

  /// Edit customer screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Customer'**
  String get editCustomer;

  /// Status label
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// Wilaya filter label
  ///
  /// In en, this message translates to:
  /// **'Wilaya'**
  String get wilaya;

  /// Date added filter label
  ///
  /// In en, this message translates to:
  /// **'Date Added'**
  String get dateAdded;

  /// Minimum appointments filter label
  ///
  /// In en, this message translates to:
  /// **'Minimum Appointments'**
  String get minimumAppointments;

  /// Minimum spent filter label
  ///
  /// In en, this message translates to:
  /// **'Minimum Spent (DA)'**
  String get minimumSpent;

  /// Select wilaya dropdown hint
  ///
  /// In en, this message translates to:
  /// **'Select wilaya'**
  String get selectWilaya;

  /// Apply filters button text
  ///
  /// In en, this message translates to:
  /// **'Apply Filters'**
  String get applyFilters;

  /// Clear all services button text
  ///
  /// In en, this message translates to:
  /// **'Clear All'**
  String get clearAll;

  /// Search input placeholder text for customers
  ///
  /// In en, this message translates to:
  /// **'Search by name, email, or phone...'**
  String get searchCustomersPlaceholder;

  /// New customer button text
  ///
  /// In en, this message translates to:
  /// **'New Customer'**
  String get newCustomer;

  /// Additional information section title
  ///
  /// In en, this message translates to:
  /// **'Additional Information'**
  String get additionalInformation;

  /// Hint text for additional notes field
  ///
  /// In en, this message translates to:
  /// **'Additional notes about the customer'**
  String get additionalNotesHint;

  /// Dialog title when customer already exists
  ///
  /// In en, this message translates to:
  /// **'Customer Already Exists'**
  String get customerAlreadyExists;

  /// Message when customer exists in archive
  ///
  /// In en, this message translates to:
  /// **'A customer with this information already exists in the archive.'**
  String get customerExistsInArchive;

  /// Question about restoring existing customer
  ///
  /// In en, this message translates to:
  /// **'Would you like to restore the existing customer instead?'**
  String get wouldYouLikeToRestore;

  /// Restore customer button text
  ///
  /// In en, this message translates to:
  /// **'Restore Customer'**
  String get restoreCustomer;

  /// Success message when customer is restored
  ///
  /// In en, this message translates to:
  /// **'Customer {firstName} {lastName} restored successfully'**
  String customerRestoredSuccessfully(String firstName, String lastName);

  /// Error message when customer restore fails
  ///
  /// In en, this message translates to:
  /// **'Failed to restore customer: {error}'**
  String failedToRestoreCustomer(String error);

  /// Success message when customer is deleted
  ///
  /// In en, this message translates to:
  /// **'Customer {firstName} {lastName} deleted successfully'**
  String customerDeletedSuccessfully(String firstName, String lastName);

  /// Error message when customer deletion fails
  ///
  /// In en, this message translates to:
  /// **'Failed to delete customer: {error}'**
  String failedToDeleteCustomer(String error);

  /// Success message when customer is updated
  ///
  /// In en, this message translates to:
  /// **'Customer {firstName} {lastName} updated successfully'**
  String customerUpdatedSuccessfully(String firstName, String lastName);

  /// Error message when customer update fails
  ///
  /// In en, this message translates to:
  /// **'Failed to update customer: {error}'**
  String failedToUpdateCustomer(String error);

  /// Search input placeholder for appointments
  ///
  /// In en, this message translates to:
  /// **'Search appointments...'**
  String get searchAppointmentsPlaceholder;

  /// Filters section title
  ///
  /// In en, this message translates to:
  /// **'Filters'**
  String get filters;

  /// All filter option
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// Filter appointments screen title
  ///
  /// In en, this message translates to:
  /// **'Filter Appointments'**
  String get filterAppointments;

  /// Filter appointments description
  ///
  /// In en, this message translates to:
  /// **'Customize your appointment view with filters'**
  String get customizeAppointmentView;

  /// Queue filter section title
  ///
  /// In en, this message translates to:
  /// **'Queue Filter'**
  String get queueFilter;

  /// Pending status text
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// Scheduled status text
  ///
  /// In en, this message translates to:
  /// **'Scheduled'**
  String get scheduled;

  /// Confirmed status text
  ///
  /// In en, this message translates to:
  /// **'Confirmed'**
  String get confirmed;

  /// In progress status text
  ///
  /// In en, this message translates to:
  /// **'In Progress'**
  String get inProgress;

  /// Completed status text
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// Canceled status text
  ///
  /// In en, this message translates to:
  /// **'Canceled'**
  String get canceled;

  /// No show status text
  ///
  /// In en, this message translates to:
  /// **'No Show'**
  String get noShow;

  /// Rescheduled status text
  ///
  /// In en, this message translates to:
  /// **'Rescheduled'**
  String get rescheduled;

  /// Confirm appointment action
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// Start service action
  ///
  /// In en, this message translates to:
  /// **'Start Service'**
  String get startService;

  /// Complete status
  ///
  /// In en, this message translates to:
  /// **'Complete'**
  String get complete;

  /// Edit appointment screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Appointment'**
  String get editAppointment;

  /// No show appointment message
  ///
  /// In en, this message translates to:
  /// **'Customer did not show up for appointment'**
  String get customerDidNotShowUp;

  /// Completed appointment message
  ///
  /// In en, this message translates to:
  /// **'Session completed successfully'**
  String get sessionCompletedSuccessfully;

  /// Date range filter label
  ///
  /// In en, this message translates to:
  /// **'Date Range'**
  String get dateRange;

  /// Start date field label
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get startDate;

  /// End date field label
  ///
  /// In en, this message translates to:
  /// **'End Date'**
  String get endDate;

  /// Status filter section title
  ///
  /// In en, this message translates to:
  /// **'Status Filter'**
  String get statusFilter;

  /// Quick date selection title
  ///
  /// In en, this message translates to:
  /// **'Quick Selection'**
  String get quickSelection;

  /// Today date option
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// This week date option
  ///
  /// In en, this message translates to:
  /// **'This Week'**
  String get thisWeek;

  /// This month date option
  ///
  /// In en, this message translates to:
  /// **'This Month'**
  String get thisMonth;

  /// Custom date range title
  ///
  /// In en, this message translates to:
  /// **'Custom Range'**
  String get customRange;

  /// Start date field placeholder
  ///
  /// In en, this message translates to:
  /// **'Select Start Date'**
  String get selectStartDate;

  /// End date field placeholder
  ///
  /// In en, this message translates to:
  /// **'Select End Date'**
  String get selectEndDate;

  /// Clear button text
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// Status label for filter summary
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get statusLabel;

  /// Date range label for filter summary
  ///
  /// In en, this message translates to:
  /// **'Date Range'**
  String get dateRangeLabel;

  /// Queue label for filter summary
  ///
  /// In en, this message translates to:
  /// **'Queue'**
  String get queueLabel;

  /// Any option for date range
  ///
  /// In en, this message translates to:
  /// **'Any'**
  String get any;

  /// Customer information section title
  ///
  /// In en, this message translates to:
  /// **'Customer Information'**
  String get customerInformation;

  /// Service details section title
  ///
  /// In en, this message translates to:
  /// **'Service Details'**
  String get serviceDetails;

  /// Scheduling section title
  ///
  /// In en, this message translates to:
  /// **'Scheduling'**
  String get scheduling;

  /// Customer field label with required indicator
  ///
  /// In en, this message translates to:
  /// **'Customer *'**
  String get customerRequired;

  /// Service field label with required indicator
  ///
  /// In en, this message translates to:
  /// **'Service *'**
  String get serviceRequired;

  /// Location field label with required indicator
  ///
  /// In en, this message translates to:
  /// **'Location *'**
  String get locationRequired;

  /// Queue field label with required indicator
  ///
  /// In en, this message translates to:
  /// **'Queue *'**
  String get queueRequired;

  /// Date field label with required indicator
  ///
  /// In en, this message translates to:
  /// **'Date *'**
  String get dateRequired;

  /// Time range field label with required indicator
  ///
  /// In en, this message translates to:
  /// **'Time Range *'**
  String get timeRangeRequired;

  /// Service dropdown label
  ///
  /// In en, this message translates to:
  /// **'Select Service'**
  String get selectService;

  /// Location dropdown label
  ///
  /// In en, this message translates to:
  /// **'Select Location'**
  String get selectLocation;

  /// Queue dropdown label
  ///
  /// In en, this message translates to:
  /// **'Select Queue'**
  String get selectQueue;

  /// Queue dropdown disabled placeholder
  ///
  /// In en, this message translates to:
  /// **'Select a location first'**
  String get selectLocationFirst;

  /// Appointment date field label
  ///
  /// In en, this message translates to:
  /// **'Appointment Date'**
  String get appointmentDate;

  /// Start time label
  ///
  /// In en, this message translates to:
  /// **'Start Time'**
  String get startTime;

  /// End time label
  ///
  /// In en, this message translates to:
  /// **'End Time'**
  String get endTime;

  /// Notes field label
  ///
  /// In en, this message translates to:
  /// **'Notes (Optional)'**
  String get notesOptional;

  /// Notes field placeholder
  ///
  /// In en, this message translates to:
  /// **'Add any additional notes...'**
  String get addNotesPlaceholder;

  /// Customer validation error message
  ///
  /// In en, this message translates to:
  /// **'Please select a customer'**
  String get pleaseSelectCustomer;

  /// Service validation error message
  ///
  /// In en, this message translates to:
  /// **'Please select a service'**
  String get pleaseSelectService;

  /// Location selection validation message
  ///
  /// In en, this message translates to:
  /// **'Please select a location'**
  String get pleaseSelectLocation;

  /// Queue validation error message
  ///
  /// In en, this message translates to:
  /// **'Please select a queue'**
  String get pleaseSelectQueue;

  /// Time validation error message
  ///
  /// In en, this message translates to:
  /// **'Start time must be before end time'**
  String get startTimeBeforeEndTime;

  /// Success message after creating appointment
  ///
  /// In en, this message translates to:
  /// **'Appointment created successfully'**
  String get appointmentCreatedSuccessfully;

  /// Appointment number prefix
  ///
  /// In en, this message translates to:
  /// **'Appointment #'**
  String get appointmentNumber;

  /// Customer label in appointment info
  ///
  /// In en, this message translates to:
  /// **'Customer'**
  String get customerLabel;

  /// Service label in appointment info
  ///
  /// In en, this message translates to:
  /// **'Service'**
  String get serviceLabel;

  /// Original time label in edit appointment
  ///
  /// In en, this message translates to:
  /// **'Original Time'**
  String get originalTime;

  /// Start time field placeholder
  ///
  /// In en, this message translates to:
  /// **'Select start time'**
  String get selectStartTime;

  /// End time field placeholder
  ///
  /// In en, this message translates to:
  /// **'Select end time'**
  String get selectEndTime;

  /// Message when no location is selected for queue
  ///
  /// In en, this message translates to:
  /// **'Please select a location first'**
  String get pleaseSelectLocationFirst;

  /// Message when no queues are available
  ///
  /// In en, this message translates to:
  /// **'No queues available for selected location'**
  String get noQueuesAvailable;

  /// Error message for location loading failure
  ///
  /// In en, this message translates to:
  /// **'Error loading locations'**
  String get errorLoadingLocations;

  /// Notes field label in edit appointment
  ///
  /// In en, this message translates to:
  /// **'Appointment notes (optional)'**
  String get appointmentNotesOptional;

  /// Duration field label
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get duration;

  /// Time section title
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// Scheduled appointment status
  ///
  /// In en, this message translates to:
  /// **'Scheduled'**
  String get statusScheduled;

  /// Confirmed appointment status
  ///
  /// In en, this message translates to:
  /// **'Confirmed'**
  String get statusConfirmed;

  /// Completed appointment status
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get statusCompleted;

  /// Canceled appointment status
  ///
  /// In en, this message translates to:
  /// **'Canceled'**
  String get statusCanceled;

  /// No show appointment status
  ///
  /// In en, this message translates to:
  /// **'No Show'**
  String get statusNoShow;

  /// Cancel appointment dialog title
  ///
  /// In en, this message translates to:
  /// **'Cancel Appointment'**
  String get cancelAppointment;

  /// Cancel appointment confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to cancel this appointment?'**
  String get cancelAppointmentConfirmation;

  /// Cancellation reason field label
  ///
  /// In en, this message translates to:
  /// **'Cancellation reason (optional)'**
  String get cancellationReason;

  /// Cancellation reason field hint
  ///
  /// In en, this message translates to:
  /// **'Enter reason for cancellation...'**
  String get cancellationReasonHint;

  /// Keep appointment button text
  ///
  /// In en, this message translates to:
  /// **'Keep Appointment'**
  String get keepAppointment;

  /// Appointment cancellation success message
  ///
  /// In en, this message translates to:
  /// **'Appointment cancelled successfully'**
  String get appointmentCancelledSuccessfully;

  /// Appointment cancellation failure message
  ///
  /// In en, this message translates to:
  /// **'Failed to cancel appointment'**
  String get failedToCancelAppointment;

  /// Calendar settings screen title
  ///
  /// In en, this message translates to:
  /// **'Calendar Settings'**
  String get calendarSettings;

  /// Calendar settings description
  ///
  /// In en, this message translates to:
  /// **'Configure calendar display preferences'**
  String get configureCalendarDisplayPreferences;

  /// Time slot interval setting label
  ///
  /// In en, this message translates to:
  /// **'Time Slot Interval'**
  String get timeSlotInterval;

  /// Time slot interval dropdown hint
  ///
  /// In en, this message translates to:
  /// **'Select interval'**
  String get selectInterval;

  /// Time slot interval explanation text
  ///
  /// In en, this message translates to:
  /// **'This setting determines how many time slots each hour is divided into. For example, 5 minutes will create 12 slots per hour, while 15 minutes creates 4 slots per hour.'**
  String get timeSlotIntervalDescription;

  /// Apply button text
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// Status and overview section title
  ///
  /// In en, this message translates to:
  /// **'Status & Overview'**
  String get statusAndOverview;

  /// Service information section title
  ///
  /// In en, this message translates to:
  /// **'Service Information'**
  String get serviceInformation;

  /// Default location name
  ///
  /// In en, this message translates to:
  /// **'Primary Location'**
  String get primaryLocation;

  /// Cancel appointment dialog title
  ///
  /// In en, this message translates to:
  /// **'Cancel Appointment'**
  String get cancelAppointmentConfirm;

  /// Cancel appointment confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to cancel the appointment with {customerName}?'**
  String cancelAppointmentMessage(String customerName);

  /// Complete appointment dialog title
  ///
  /// In en, this message translates to:
  /// **'Complete Appointment'**
  String get completeAppointmentTitle;

  /// Complete appointment confirmation message
  ///
  /// In en, this message translates to:
  /// **'Mark the appointment with {customerName} as completed?'**
  String completeAppointmentMessage(String customerName);

  /// Appointment confirmation success message
  ///
  /// In en, this message translates to:
  /// **'Appointment confirmed for {customerName}'**
  String appointmentConfirmedFor(String customerName);

  /// Appointment cancellation success message
  ///
  /// In en, this message translates to:
  /// **'Appointment with {customerName} canceled'**
  String appointmentCanceledFor(String customerName);

  /// Customer label
  ///
  /// In en, this message translates to:
  /// **'Customer'**
  String get customer;

  /// Service label
  ///
  /// In en, this message translates to:
  /// **'Service'**
  String get service;

  /// Location label
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// Profile overview section title
  ///
  /// In en, this message translates to:
  /// **'Profile Overview'**
  String get profileOverview;

  /// Statistics section title
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get statistics;

  /// Rating label
  ///
  /// In en, this message translates to:
  /// **'Rating'**
  String get rating;

  /// Reviews label
  ///
  /// In en, this message translates to:
  /// **'Reviews'**
  String get reviews;

  /// Setup status label
  ///
  /// In en, this message translates to:
  /// **'Setup Status'**
  String get setupStatus;

  /// Incomplete status
  ///
  /// In en, this message translates to:
  /// **'Incomplete'**
  String get incomplete;

  /// Business logo section title
  ///
  /// In en, this message translates to:
  /// **'Business Logo'**
  String get businessLogo;

  /// Default provider title
  ///
  /// In en, this message translates to:
  /// **'Provider'**
  String get provider;

  /// Verified status badge
  ///
  /// In en, this message translates to:
  /// **'Verified'**
  String get verified;

  /// Change logo button text
  ///
  /// In en, this message translates to:
  /// **'Change Logo'**
  String get changeLogo;

  /// Upload logo button text
  ///
  /// In en, this message translates to:
  /// **'Upload Logo'**
  String get uploadLogo;

  /// Logo upload format information
  ///
  /// In en, this message translates to:
  /// **'Supported formats: PNG, JPG, JPEG • Max size: 5MB'**
  String get supportedFormats;

  /// No logo placeholder text
  ///
  /// In en, this message translates to:
  /// **'No logo uploaded'**
  String get noLogoUploaded;

  /// Upload business logo dialog title
  ///
  /// In en, this message translates to:
  /// **'Upload Business Logo'**
  String get uploadBusinessLogo;

  /// Error loading profile title
  ///
  /// In en, this message translates to:
  /// **'Error Loading Profile'**
  String get errorLoadingProfile;

  /// Profile and business images section title
  ///
  /// In en, this message translates to:
  /// **'Profile & Business Images'**
  String get profileAndBusinessImages;

  /// Professional title field label
  ///
  /// In en, this message translates to:
  /// **'Professional Title'**
  String get professionalTitle;

  /// Actions section title
  ///
  /// In en, this message translates to:
  /// **'Actions'**
  String get actions;

  /// Profile picture label
  ///
  /// In en, this message translates to:
  /// **'Profile Picture'**
  String get profilePicture;

  /// Title field validation message
  ///
  /// In en, this message translates to:
  /// **'Title is required'**
  String get titleRequired;

  /// Phone number field validation message
  ///
  /// In en, this message translates to:
  /// **'Phone number is required'**
  String get phoneRequired;

  /// No category selected placeholder
  ///
  /// In en, this message translates to:
  /// **'No category selected'**
  String get noCategorySelected;

  /// Category field disabled message
  ///
  /// In en, this message translates to:
  /// **'Category cannot be changed'**
  String get categoryCannotBeChanged;

  /// Saving progress text
  ///
  /// In en, this message translates to:
  /// **'Saving...'**
  String get saving;

  /// Save changes button text
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChanges;

  /// Profile update success message
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profileUpdatedSuccessfully;

  /// Profile update error message
  ///
  /// In en, this message translates to:
  /// **'Failed to update profile: {error}'**
  String failedToUpdateProfile(String error);

  /// Profile picture update success message
  ///
  /// In en, this message translates to:
  /// **'Profile picture updated successfully!'**
  String get profilePictureUpdatedSuccessfully;

  /// Profile completion widget title
  ///
  /// In en, this message translates to:
  /// **'Profile Completion'**
  String get profileCompletion;

  /// Overall progress section title
  ///
  /// In en, this message translates to:
  /// **'Overall Progress'**
  String get overallProgress;

  /// Percentage complete text
  ///
  /// In en, this message translates to:
  /// **'{percentage}% Complete'**
  String percentComplete(int percentage);

  /// Details section title
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// Profile picture completion item
  ///
  /// In en, this message translates to:
  /// **'Profile Picture'**
  String get profilePictureItem;

  /// Provider info completion item
  ///
  /// In en, this message translates to:
  /// **'Provider Info'**
  String get providerInfo;

  /// Next steps section title
  ///
  /// In en, this message translates to:
  /// **'Next Steps'**
  String get nextSteps;

  /// Error message for profile completion loading failure
  ///
  /// In en, this message translates to:
  /// **'Failed to load profile completion'**
  String get failedToLoadProfileCompletion;

  /// Next step: upload profile picture
  ///
  /// In en, this message translates to:
  /// **'Upload a professional profile picture'**
  String get uploadProfessionalProfilePicture;

  /// Next step: complete provider info
  ///
  /// In en, this message translates to:
  /// **'Complete provider information: business description'**
  String get completeProviderInformation;

  /// Next step: add service location
  ///
  /// In en, this message translates to:
  /// **'Add at least one service location'**
  String get addAtLeastOneServiceLocation;

  /// Next step: create service offering
  ///
  /// In en, this message translates to:
  /// **'Create at least one service offering'**
  String get createAtLeastOneServiceOffering;

  /// Next step: set up booking queues
  ///
  /// In en, this message translates to:
  /// **'Set up booking queues/time slots'**
  String get setUpBookingQueuesTimeSlots;

  /// Customer profile screen title
  ///
  /// In en, this message translates to:
  /// **'Customer Profile'**
  String get customerProfile;

  /// Delete customer dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Customer'**
  String get deleteCustomer;

  /// Delete customer confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete {firstName} {lastName}? This action cannot be undone.'**
  String deleteCustomerConfirmation(String firstName, String lastName);

  /// Information section title
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get information;

  /// Mobile phone field label
  ///
  /// In en, this message translates to:
  /// **'Mobile'**
  String get mobile;

  /// Customer since label
  ///
  /// In en, this message translates to:
  /// **'Customer Since'**
  String get customerSince;

  /// Customer statistics section title
  ///
  /// In en, this message translates to:
  /// **'Customer Statistics'**
  String get customerStatistics;

  /// Total spent label
  ///
  /// In en, this message translates to:
  /// **'Total Spent'**
  String get totalSpent;

  /// Last visit label
  ///
  /// In en, this message translates to:
  /// **'Last Visit'**
  String get lastVisit;

  /// Never visited label
  ///
  /// In en, this message translates to:
  /// **'Never'**
  String get never;

  /// Recent appointments section title
  ///
  /// In en, this message translates to:
  /// **'Recent Appointments'**
  String get recentAppointments;

  /// No recent appointments message
  ///
  /// In en, this message translates to:
  /// **'No recent appointments'**
  String get noRecentAppointments;

  /// Add appointment coming soon message
  ///
  /// In en, this message translates to:
  /// **'Add appointment feature coming soon'**
  String get addAppointmentFeatureComingSoon;

  /// Loading dashboard message
  ///
  /// In en, this message translates to:
  /// **'Loading dashboard...'**
  String get loadingDashboard;

  /// Failed to load dashboard error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load dashboard'**
  String get failedToLoadDashboard;

  /// Unknown error message
  ///
  /// In en, this message translates to:
  /// **'Unknown error occurred'**
  String get unknownErrorOccurred;

  /// Refreshing data message
  ///
  /// In en, this message translates to:
  /// **'Refreshing data...'**
  String get refreshingData;

  /// Failed to load active sessions error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load active sessions'**
  String get failedToLoadActiveSessions;

  /// Failed to load pending appointments error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load pending appointments'**
  String get failedToLoadPendingAppointments;

  /// Processing emergency control message
  ///
  /// In en, this message translates to:
  /// **'Processing emergency control...'**
  String get processingEmergencyControl;

  /// Refresh service tooltip
  ///
  /// In en, this message translates to:
  /// **'Refresh Service'**
  String get refreshService;

  /// Edit service screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Service'**
  String get editService;

  /// Location name field label
  ///
  /// In en, this message translates to:
  /// **'Location Name'**
  String get locationName;

  /// Location name field hint
  ///
  /// In en, this message translates to:
  /// **'e.g., Main Office, Branch Downtown'**
  String get locationNameHint;

  /// Location name validation error
  ///
  /// In en, this message translates to:
  /// **'Location name is required'**
  String get locationNameRequired;

  /// Location name minimum length validation error
  ///
  /// In en, this message translates to:
  /// **'Location name must be at least 2 characters'**
  String get locationNameMinLength;

  /// Location name maximum length validation error
  ///
  /// In en, this message translates to:
  /// **'Location name must be less than 100 characters'**
  String get locationNameMaxLength;

  /// Street address field label
  ///
  /// In en, this message translates to:
  /// **'Street Address'**
  String get streetAddress;

  /// Street address field hint text
  ///
  /// In en, this message translates to:
  /// **'e.g., 123 Main Street'**
  String get streetAddressHint;

  /// Street address validation error
  ///
  /// In en, this message translates to:
  /// **'Street address is required'**
  String get streetAddressRequired;

  /// Address completeness validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a complete address'**
  String get pleaseEnterCompleteAddress;

  /// Address maximum length validation error
  ///
  /// In en, this message translates to:
  /// **'Address must be less than 200 characters'**
  String get addressMaxLength;

  /// Country field label
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// Algeria country name
  ///
  /// In en, this message translates to:
  /// **'Algeria'**
  String get algeria;

  /// Monday day name
  ///
  /// In en, this message translates to:
  /// **'Monday'**
  String get monday;

  /// Tuesday day name
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get tuesday;

  /// Wednesday day name
  ///
  /// In en, this message translates to:
  /// **'Wednesday'**
  String get wednesday;

  /// Thursday day name
  ///
  /// In en, this message translates to:
  /// **'Thursday'**
  String get thursday;

  /// Friday day name
  ///
  /// In en, this message translates to:
  /// **'Friday'**
  String get friday;

  /// Saturday day name
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get saturday;

  /// Sunday day name
  ///
  /// In en, this message translates to:
  /// **'Sunday'**
  String get sunday;

  /// Active service sessions card title
  ///
  /// In en, this message translates to:
  /// **'Active Service Sessions'**
  String get activeServiceSessions;

  /// View all button text
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// No active sessions message
  ///
  /// In en, this message translates to:
  /// **'No Active Sessions'**
  String get noActiveSessions;

  /// No active sessions description
  ///
  /// In en, this message translates to:
  /// **'There are no active service sessions at the moment'**
  String get noActiveSessionsDescription;

  /// Pending appointments card title
  ///
  /// In en, this message translates to:
  /// **'Pending Appointments'**
  String get pendingAppointments;

  /// All caught up message
  ///
  /// In en, this message translates to:
  /// **'All Caught Up!'**
  String get allCaughtUp;

  /// No pending appointments message
  ///
  /// In en, this message translates to:
  /// **'No pending appointments to review'**
  String get noPendingAppointments;

  /// Today's schedule card title
  ///
  /// In en, this message translates to:
  /// **'Today\'s Schedule'**
  String get todaysSchedule;

  /// Error message when unable to refresh data
  ///
  /// In en, this message translates to:
  /// **'Unable to refresh data. Showing cached information.'**
  String get unableToRefreshData;

  /// Morning greeting
  ///
  /// In en, this message translates to:
  /// **'Good morning'**
  String get goodMorning;

  /// Afternoon greeting
  ///
  /// In en, this message translates to:
  /// **'Good afternoon'**
  String get goodAfternoon;

  /// Evening greeting
  ///
  /// In en, this message translates to:
  /// **'Good evening'**
  String get goodEvening;

  /// Waiting status text
  ///
  /// In en, this message translates to:
  /// **'waiting'**
  String get waiting;

  /// More queues text
  ///
  /// In en, this message translates to:
  /// **'+{count} more queues'**
  String moreQueues(int count);

  /// No active queues message
  ///
  /// In en, this message translates to:
  /// **'No active queues'**
  String get noActiveQueues;

  /// Total label
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// Upcoming label
  ///
  /// In en, this message translates to:
  /// **'Upcoming'**
  String get upcoming;

  /// Appointments awaiting action message
  ///
  /// In en, this message translates to:
  /// **'{count} appointment{plural} awaiting action'**
  String appointmentsAwaitingAction(int count, String plural);

  /// Quick actions section title
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get quickActions;

  /// New service button text
  ///
  /// In en, this message translates to:
  /// **'New Service'**
  String get newService;

  /// New queue button text
  ///
  /// In en, this message translates to:
  /// **'New Queue'**
  String get newQueue;

  /// New appointment button text
  ///
  /// In en, this message translates to:
  /// **'New Appointment'**
  String get newAppointment;

  /// No upcoming appointments message
  ///
  /// In en, this message translates to:
  /// **'No upcoming appointments'**
  String get noUpcomingAppointments;

  /// All appointments completed message
  ///
  /// In en, this message translates to:
  /// **'All appointments for today are completed'**
  String get allAppointmentsCompleted;

  /// Today's summary section title
  ///
  /// In en, this message translates to:
  /// **'Today\'s Summary'**
  String get todaysSummary;

  /// Updated seconds ago message
  ///
  /// In en, this message translates to:
  /// **'Updated {seconds}s ago'**
  String updatedSecondsAgo(int seconds);

  /// Updated minutes ago message
  ///
  /// In en, this message translates to:
  /// **'Updated {minutes}m ago'**
  String updatedMinutesAgo(int minutes);

  /// Updated hours ago message
  ///
  /// In en, this message translates to:
  /// **'Updated {hours}h ago'**
  String updatedHoursAgo(int hours);

  /// Updated days ago message
  ///
  /// In en, this message translates to:
  /// **'Updated {days}d ago'**
  String updatedDaysAgo(int days);

  /// Just now time indicator
  ///
  /// In en, this message translates to:
  /// **'Just now'**
  String get justNow;

  /// Data refresh section title
  ///
  /// In en, this message translates to:
  /// **'Data Refresh'**
  String get dataRefresh;

  /// Last updated message
  ///
  /// In en, this message translates to:
  /// **'Last updated: {timeAgo}'**
  String lastUpdated(String timeAgo);

  /// By location tab label
  ///
  /// In en, this message translates to:
  /// **'By Location'**
  String get byLocation;

  /// All queues tab label
  ///
  /// In en, this message translates to:
  /// **'All Queues'**
  String get allQueues;

  /// Schedule management screen title
  ///
  /// In en, this message translates to:
  /// **'Schedule Management'**
  String get scheduleManagement;

  /// Weekly view tab label
  ///
  /// In en, this message translates to:
  /// **'Weekly View'**
  String get weeklyView;

  /// List view tab label
  ///
  /// In en, this message translates to:
  /// **'List View'**
  String get listView;

  /// App settings section header
  ///
  /// In en, this message translates to:
  /// **'App Settings'**
  String get appSettings;

  /// Account section header
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get account;

  /// Management section header
  ///
  /// In en, this message translates to:
  /// **'Management'**
  String get management;

  /// Services description text
  ///
  /// In en, this message translates to:
  /// **'Manage your service offerings'**
  String get manageServiceOfferings;

  /// Locations description text
  ///
  /// In en, this message translates to:
  /// **'Configure business locations'**
  String get configureBusinessLocations;

  /// Queues description text
  ///
  /// In en, this message translates to:
  /// **'Set up appointment queues'**
  String get setupAppointmentQueues;

  /// Help and support description text
  ///
  /// In en, this message translates to:
  /// **'Get help and support'**
  String get getHelpAndSupport;

  /// Logout description text
  ///
  /// In en, this message translates to:
  /// **'Sign out of your account'**
  String get signOutOfAccount;

  /// Queue management screen title
  ///
  /// In en, this message translates to:
  /// **'Queue Management'**
  String get queueManagement;

  /// Create service screen title
  ///
  /// In en, this message translates to:
  /// **'Create Service'**
  String get createService;

  /// Create service header title
  ///
  /// In en, this message translates to:
  /// **'Create New Service'**
  String get createNewService;

  /// Create service header description
  ///
  /// In en, this message translates to:
  /// **'Add a new service to your business offerings'**
  String get addNewServiceDescription;

  /// Service creation info message
  ///
  /// In en, this message translates to:
  /// **'Your service will be available for booking once created. You can edit or disable it later from the services list.'**
  String get serviceAvailableInfo;

  /// Creating service loading text
  ///
  /// In en, this message translates to:
  /// **'Creating Service...'**
  String get creatingService;

  /// Description field label
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Description field hint
  ///
  /// In en, this message translates to:
  /// **'Describe your service'**
  String get describeYourService;

  /// Very short minutes abbreviation for dropdowns
  ///
  /// In en, this message translates to:
  /// **'m'**
  String get minutesShort;

  /// Hours abbreviation
  ///
  /// In en, this message translates to:
  /// **'h'**
  String get hoursShort;

  /// Create location screen title
  ///
  /// In en, this message translates to:
  /// **'Create Location'**
  String get createLocation;

  /// Name minimum length validation error
  ///
  /// In en, this message translates to:
  /// **'Name must be at least 2 characters'**
  String get nameMinLength;

  /// Name maximum length validation error
  ///
  /// In en, this message translates to:
  /// **'Name must be less than 100 characters'**
  String get nameMaxLength;

  /// Short name field label
  ///
  /// In en, this message translates to:
  /// **'Short Name'**
  String get shortName;

  /// Short name field hint
  ///
  /// In en, this message translates to:
  /// **'e.g., ABC Clinic, XYZ Salon'**
  String get shortNameHint;

  /// Short name validation error
  ///
  /// In en, this message translates to:
  /// **'Short name must be less than 100 characters'**
  String get shortNameMaxLength;

  /// City field label
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get city;

  /// City validation error
  ///
  /// In en, this message translates to:
  /// **'City is required'**
  String get cityRequired;

  /// City validation error for invalid selection
  ///
  /// In en, this message translates to:
  /// **'Please select a valid Algerian city'**
  String get selectValidCity;

  /// City dropdown placeholder text
  ///
  /// In en, this message translates to:
  /// **'Select an Algerian city'**
  String get selectAlgerianCity;

  /// Country validation error
  ///
  /// In en, this message translates to:
  /// **'Country is required'**
  String get countryRequired;

  /// Country validation error for non-Algeria
  ///
  /// In en, this message translates to:
  /// **'Currently only available in Algeria'**
  String get onlyAvailableInAlgeria;

  /// Location creation success message
  ///
  /// In en, this message translates to:
  /// **'Location \"{locationName}\" created successfully'**
  String locationCreatedSuccessfully(String locationName);

  /// Location creation failure message
  ///
  /// In en, this message translates to:
  /// **'Failed to create location'**
  String get failedToCreateLocation;

  /// Location update success message
  ///
  /// In en, this message translates to:
  /// **'Location \"{locationName}\" updated successfully'**
  String locationUpdatedSuccessfully(String locationName);

  /// Location update failure message
  ///
  /// In en, this message translates to:
  /// **'Failed to update location'**
  String get failedToUpdateLocation;

  /// Create new location header title
  ///
  /// In en, this message translates to:
  /// **'Create New Location'**
  String get createNewLocation;

  /// Create new location header description
  ///
  /// In en, this message translates to:
  /// **'Add a new business location with address and operating hours'**
  String get addNewBusinessLocation;

  /// Location information section header
  ///
  /// In en, this message translates to:
  /// **'Location Information'**
  String get locationInformation;

  /// Address field label
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// Address field hint
  ///
  /// In en, this message translates to:
  /// **'e.g., 123 Main Street, Building A'**
  String get addressHint;

  /// Address validation error
  ///
  /// In en, this message translates to:
  /// **'Address is required'**
  String get addressRequired;

  /// Postal code field label
  ///
  /// In en, this message translates to:
  /// **'Postal Code'**
  String get postalCode;

  /// Postal code field hint
  ///
  /// In en, this message translates to:
  /// **'e.g., 16000 (optional)'**
  String get postalCodeHint;

  /// Timezone field label
  ///
  /// In en, this message translates to:
  /// **'Timezone'**
  String get timezone;

  /// Timezone validation error
  ///
  /// In en, this message translates to:
  /// **'Timezone is required'**
  String get timezoneRequired;

  /// Location coordinates section header
  ///
  /// In en, this message translates to:
  /// **'Location Coordinates'**
  String get locationCoordinates;

  /// Location coordinates description
  ///
  /// In en, this message translates to:
  /// **'Get your current location to help customers find you easily.'**
  String get getCurrentLocationDescription;

  /// Latitude field label
  ///
  /// In en, this message translates to:
  /// **'Latitude'**
  String get latitude;

  /// Longitude field label
  ///
  /// In en, this message translates to:
  /// **'Longitude'**
  String get longitude;

  /// Coordinates field hint
  ///
  /// In en, this message translates to:
  /// **'Will be filled automatically'**
  String get willBeFilledAutomatically;

  /// Coordinates validation error
  ///
  /// In en, this message translates to:
  /// **'Please get current location'**
  String get pleaseGetCurrentLocation;

  /// Latitude validation error
  ///
  /// In en, this message translates to:
  /// **'Invalid latitude'**
  String get invalidLatitude;

  /// Longitude validation error
  ///
  /// In en, this message translates to:
  /// **'Invalid longitude'**
  String get invalidLongitude;

  /// Latitude range validation error
  ///
  /// In en, this message translates to:
  /// **'Latitude must be between -90 and 90'**
  String get latitudeMustBeBetween;

  /// Longitude range validation error
  ///
  /// In en, this message translates to:
  /// **'Longitude must be between -180 and 180'**
  String get longitudeMustBeBetween;

  /// Get current location button text
  ///
  /// In en, this message translates to:
  /// **'Get Current Location'**
  String get getCurrentLocation;

  /// Amenities section header
  ///
  /// In en, this message translates to:
  /// **'Amenities'**
  String get amenities;

  /// Parking amenity label
  ///
  /// In en, this message translates to:
  /// **'Parking Available'**
  String get parkingAvailable;

  /// Parking amenity description
  ///
  /// In en, this message translates to:
  /// **'On-site parking for customers'**
  String get onsiteParkingForCustomers;

  /// Elevator amenity label
  ///
  /// In en, this message translates to:
  /// **'Elevator Access'**
  String get elevatorAccess;

  /// Elevator amenity description
  ///
  /// In en, this message translates to:
  /// **'Building has elevator access'**
  String get buildingHasElevatorAccess;

  /// Wheelchair accessibility amenity label
  ///
  /// In en, this message translates to:
  /// **'Wheelchair Accessible'**
  String get wheelchairAccessible;

  /// Wheelchair accessibility amenity description
  ///
  /// In en, this message translates to:
  /// **'Accessible for people with disabilities'**
  String get accessibleForPeopleWithDisabilities;

  /// Opening hours section header
  ///
  /// In en, this message translates to:
  /// **'Opening Hours'**
  String get openingHours;

  /// Day status when closed
  ///
  /// In en, this message translates to:
  /// **'Closed'**
  String get closed;

  /// Day status when open
  ///
  /// In en, this message translates to:
  /// **'Open'**
  String get open;

  /// Opening hours tip text
  ///
  /// In en, this message translates to:
  /// **'Tip: Toggle the switch to mark a day as open or closed'**
  String get tipToggleSwitchDayOpenClosed;

  /// Location creation info card text
  ///
  /// In en, this message translates to:
  /// **'This location will be added to your business locations. You can manage all locations from the locations section'**
  String get locationWillBeAddedToBusinessLocations;

  /// Location creation guidance text
  ///
  /// In en, this message translates to:
  /// **'Your location will be available for queue and appointment management once created. Make sure to set accurate coordinates and operating hours'**
  String get locationAvailableForQueueManagement;

  /// Creating location loading text
  ///
  /// In en, this message translates to:
  /// **'Creating Location...'**
  String get creatingLocation;

  /// Edit location screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Location'**
  String get editLocation;

  /// Edit location header description
  ///
  /// In en, this message translates to:
  /// **'Update location details, address, and operating hours'**
  String get updateLocationDetails;

  /// Location coordinates update description
  ///
  /// In en, this message translates to:
  /// **'Update coordinates if the location has moved to help customers find you easily'**
  String get updateCoordinatesDescription;

  /// Edit location info card text about service updates
  ///
  /// In en, this message translates to:
  /// **'Changes to this location will be updated across all your business services and queues'**
  String get changesWillBeUpdatedAcrossServices;

  /// Edit location info card text about immediate saving
  ///
  /// In en, this message translates to:
  /// **'Changes will be saved immediately and reflected in your location settings. Existing queues and appointments will not be affected'**
  String get changesWillBeSavedImmediately;

  /// Saving changes loading text
  ///
  /// In en, this message translates to:
  /// **'Saving Changes...'**
  String get savingChanges;

  /// Update current location button text
  ///
  /// In en, this message translates to:
  /// **'Update Current Location'**
  String get updateCurrentLocation;

  /// Fax field label
  ///
  /// In en, this message translates to:
  /// **'Fax'**
  String get fax;

  /// Floor field label
  ///
  /// In en, this message translates to:
  /// **'Floor'**
  String get floor;

  /// Mobile phone field hint text
  ///
  /// In en, this message translates to:
  /// **'e.g., +213 555-123456'**
  String get mobileHint;

  /// Fax field hint text
  ///
  /// In en, this message translates to:
  /// **'e.g., +213 555-123456'**
  String get faxHint;

  /// Floor field hint text
  ///
  /// In en, this message translates to:
  /// **'e.g., 5th Floor'**
  String get floorHint;

  /// Create queue header title
  ///
  /// In en, this message translates to:
  /// **'Create Queue'**
  String get createQueue;

  /// Edit queue header title
  ///
  /// In en, this message translates to:
  /// **'Edit Queue'**
  String get editQueue;

  /// Queue information section header
  ///
  /// In en, this message translates to:
  /// **'Queue Information'**
  String get queueInformation;

  /// Queue name field label
  ///
  /// In en, this message translates to:
  /// **'Queue Name'**
  String get queueName;

  /// Queue name field hint text
  ///
  /// In en, this message translates to:
  /// **'e.g., General Queue, VIP Queue, Walk-ins'**
  String get queueNameHint;

  /// Queue services section header
  ///
  /// In en, this message translates to:
  /// **'Queue Services'**
  String get queueServices;

  /// Queue services selection description
  ///
  /// In en, this message translates to:
  /// **'Select services that will be available in this queue'**
  String get selectServicesDescription;

  /// Select all services button text
  ///
  /// In en, this message translates to:
  /// **'Select All'**
  String get selectAll;

  /// Services selection counter text
  ///
  /// In en, this message translates to:
  /// **'{count} of {total} selected'**
  String servicesSelected(int count, int total);

  /// Queue name required validation message
  ///
  /// In en, this message translates to:
  /// **'Queue name is required'**
  String get queueNameRequired;

  /// Queue name minimum length validation message
  ///
  /// In en, this message translates to:
  /// **'Name must be at least 2 characters'**
  String get queueNameMinLength;

  /// Queue name maximum length validation message
  ///
  /// In en, this message translates to:
  /// **'Queue name cannot exceed 100 characters'**
  String get queueNameMaxLength;

  /// Loading services text
  ///
  /// In en, this message translates to:
  /// **'Loading services...'**
  String get loadingServices;

  /// Credits text label
  ///
  /// In en, this message translates to:
  /// **'credits'**
  String get credits;

  /// Service selection validation message
  ///
  /// In en, this message translates to:
  /// **'Please select at least one service'**
  String get pleaseSelectAtLeastOneService;

  /// Queue operating hours section header
  ///
  /// In en, this message translates to:
  /// **'Queue Operating Hours'**
  String get queueOperatingHours;

  /// Queue operating hours description
  ///
  /// In en, this message translates to:
  /// **'Set when this queue is available for appointments'**
  String get setQueueAvailabilityDescription;

  /// No services available message
  ///
  /// In en, this message translates to:
  /// **'No services available'**
  String get noServicesAvailable;

  /// Create services first instruction message
  ///
  /// In en, this message translates to:
  /// **'Create services first to assign them to queues.'**
  String get createServicesFirstMessage;

  /// Add services button text
  ///
  /// In en, this message translates to:
  /// **'Add Services'**
  String get addServices;

  /// Service delivery type field label
  ///
  /// In en, this message translates to:
  /// **'Service Delivery Type'**
  String get serviceDeliveryType;

  /// Delivery type option - at business
  ///
  /// In en, this message translates to:
  /// **'At Business Location'**
  String get atBusinessLocation;

  /// Delivery type option - at customer
  ///
  /// In en, this message translates to:
  /// **'At Customer Location'**
  String get atCustomerLocation;

  /// Delivery type option - both locations
  ///
  /// In en, this message translates to:
  /// **'Both Options'**
  String get bothOptions;

  /// Delivery type validation error
  ///
  /// In en, this message translates to:
  /// **'Please select a delivery type'**
  String get selectDeliveryType;

  /// Regions selection placeholder
  ///
  /// In en, this message translates to:
  /// **'Select regions...'**
  String get selectRegions;

  /// Regions selection count
  ///
  /// In en, this message translates to:
  /// **'{count} region(s) selected'**
  String regionsSelected(int count);

  /// Custom color input field label
  ///
  /// In en, this message translates to:
  /// **'Custom Color'**
  String get customColor;

  /// Hex color validation error
  ///
  /// In en, this message translates to:
  /// **'Invalid hex color'**
  String get invalidHexColor;

  /// Edit service screen title with service name
  ///
  /// In en, this message translates to:
  /// **'Edit {serviceName}'**
  String editServiceTitle(String serviceName);

  /// Edit service header description
  ///
  /// In en, this message translates to:
  /// **'Update \"{serviceName}\" details and settings'**
  String updateServiceDescription(String serviceName);

  /// Edit service info message
  ///
  /// In en, this message translates to:
  /// **'Changes will be saved immediately and reflected in your service offerings. Existing appointments will not be affected.'**
  String get changesWillBeSaved;

  /// Error loading service message
  ///
  /// In en, this message translates to:
  /// **'Error loading service: {error}'**
  String errorLoadingService(String error);

  /// Error refreshing service message
  ///
  /// In en, this message translates to:
  /// **'Error refreshing service: {error}'**
  String errorRefreshingService(String error);

  /// Service update success message
  ///
  /// In en, this message translates to:
  /// **'Service \"{serviceName}\" updated successfully'**
  String serviceUpdatedSuccessfully(String serviceName);

  /// Service update failure message
  ///
  /// In en, this message translates to:
  /// **'Failed to update service'**
  String get failedToUpdateService;

  /// Error updating service message
  ///
  /// In en, this message translates to:
  /// **'Error updating service: {error}'**
  String errorUpdatingService(String error);

  /// Price field label
  ///
  /// In en, this message translates to:
  /// **'Price (DA)'**
  String get price;

  /// Price validation error
  ///
  /// In en, this message translates to:
  /// **'Price is required'**
  String get priceRequired;

  /// Price validation error
  ///
  /// In en, this message translates to:
  /// **'Enter valid price'**
  String get enterValidPrice;

  /// Service delivery section title
  ///
  /// In en, this message translates to:
  /// **'Service Delivery'**
  String get serviceDelivery;

  /// Service delivery subtitle
  ///
  /// In en, this message translates to:
  /// **'Where do you provide this service?'**
  String get whereProvideService;

  /// Appearance section title
  ///
  /// In en, this message translates to:
  /// **'Appearance'**
  String get appearance;

  /// Service options section title
  ///
  /// In en, this message translates to:
  /// **'Service Options'**
  String get serviceOptions;

  /// Public service checkbox title
  ///
  /// In en, this message translates to:
  /// **'Public Service'**
  String get publicService;

  /// Public service checkbox subtitle
  ///
  /// In en, this message translates to:
  /// **'Visible to all customers'**
  String get visibleToAllCustomers;

  /// Online bookings checkbox title
  ///
  /// In en, this message translates to:
  /// **'Accept Online Bookings'**
  String get acceptOnlineBookings;

  /// Online bookings checkbox subtitle
  ///
  /// In en, this message translates to:
  /// **'Allow customers to book online'**
  String get allowCustomersBookOnline;

  /// New customers checkbox title
  ///
  /// In en, this message translates to:
  /// **'Accept New Customers'**
  String get acceptNewCustomers;

  /// New customers checkbox subtitle
  ///
  /// In en, this message translates to:
  /// **'Allow new customers to book'**
  String get allowNewCustomersBook;

  /// Notifications checkbox title
  ///
  /// In en, this message translates to:
  /// **'Enable Notifications'**
  String get enableNotifications;

  /// Notifications checkbox subtitle
  ///
  /// In en, this message translates to:
  /// **'Get notified for new bookings'**
  String get getNotifiedNewBookings;

  /// Active checkbox subtitle
  ///
  /// In en, this message translates to:
  /// **'Service is available for booking'**
  String get serviceAvailableForBooking;

  /// Served regions validation error
  ///
  /// In en, this message translates to:
  /// **'Please select served regions for customer location services'**
  String get selectServedRegionsError;

  /// Light theme mode label
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get themeModeLight;

  /// Dark theme mode label
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get themeModeDark;

  /// System theme mode label
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get themeModeSystem;

  /// Search services placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search services...'**
  String get searchServices;

  /// No search results message
  ///
  /// In en, this message translates to:
  /// **'Try adjusting your search terms'**
  String get tryAdjustingSearchTerms;

  /// No services found message
  ///
  /// In en, this message translates to:
  /// **'No services found'**
  String get noServicesFound;

  /// Error loading services message
  ///
  /// In en, this message translates to:
  /// **'Error loading services'**
  String get errorLoadingServices;

  /// No locations found message
  ///
  /// In en, this message translates to:
  /// **'No locations found'**
  String get noLocationsFound;

  /// Failed to load locations error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load locations'**
  String get failedToLoadLocations;

  /// No locations available message
  ///
  /// In en, this message translates to:
  /// **'No Locations Available'**
  String get noLocationsAvailable;

  /// No customers found message
  ///
  /// In en, this message translates to:
  /// **'No customers found'**
  String get noCustomersFound;

  /// Failed to load customers error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load customers'**
  String get failedToLoadCustomers;

  /// Add first customer message
  ///
  /// In en, this message translates to:
  /// **'Add your first customer to get started'**
  String get addFirstCustomerToGetStarted;

  /// Add customer button text
  ///
  /// In en, this message translates to:
  /// **'Add Customer'**
  String get addCustomer;

  /// No appointments found message
  ///
  /// In en, this message translates to:
  /// **'No appointments found'**
  String get noAppointmentsFound;

  /// Try adjusting filters message
  ///
  /// In en, this message translates to:
  /// **'Try adjusting your filters'**
  String get tryAdjustingYourFilters;

  /// Create first appointment message
  ///
  /// In en, this message translates to:
  /// **'Create your first appointment to get started'**
  String get createFirstAppointmentToGetStarted;

  /// Add appointment button text
  ///
  /// In en, this message translates to:
  /// **'Add Appointment'**
  String get addAppointment;

  /// No appointments scheduled message
  ///
  /// In en, this message translates to:
  /// **'No appointments scheduled'**
  String get noAppointmentsScheduled;

  /// Instruction to add appointment
  ///
  /// In en, this message translates to:
  /// **'Tap the + button to add an appointment'**
  String get tapPlusButtonToAddAppointment;

  /// Tap to change instruction
  ///
  /// In en, this message translates to:
  /// **'Tap to change'**
  String get tapToChange;

  /// Email or phone field label
  ///
  /// In en, this message translates to:
  /// **'Email or Phone'**
  String get emailOrPhone;

  /// Email or phone field hint
  ///
  /// In en, this message translates to:
  /// **'Enter your email or phone number'**
  String get enterEmailOrPhone;

  /// Email or phone validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter your email or phone'**
  String get pleaseEnterEmailOrPhone;

  /// Password validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter your password'**
  String get pleaseEnterPassword;

  /// Join Dalti Provider title
  ///
  /// In en, this message translates to:
  /// **'Join Dalti Provider'**
  String get joinDaltiProvider;

  /// Create business account subtitle
  ///
  /// In en, this message translates to:
  /// **'Create your business account'**
  String get createBusinessAccount;

  /// Business category dropdown hint
  ///
  /// In en, this message translates to:
  /// **'Select your business category'**
  String get selectBusinessCategory;

  /// Create account button text
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get createAccount;

  /// Business name validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter your business name'**
  String get pleaseEnterBusinessName;

  /// Business category field label
  ///
  /// In en, this message translates to:
  /// **'Business Category'**
  String get businessCategory;

  /// Email validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter your email'**
  String get pleaseEnterEmail;

  /// Phone number validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter your phone number'**
  String get pleaseEnterPhoneNumber;

  /// Confirm password validation error
  ///
  /// In en, this message translates to:
  /// **'Please confirm your password'**
  String get pleaseConfirmPassword;

  /// Reset password description
  ///
  /// In en, this message translates to:
  /// **'Enter your email address and we\'ll send you a verification code to reset your password'**
  String get resetPasswordDescription;

  /// Email address field label
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddress;

  /// Email address field hint
  ///
  /// In en, this message translates to:
  /// **'Enter your email address'**
  String get enterEmailAddress;

  /// Email address validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter your email address'**
  String get pleaseEnterEmailAddress;

  /// Valid email address validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get pleaseEnterValidEmailAddress;

  /// Send reset code button text
  ///
  /// In en, this message translates to:
  /// **'Send Reset Code'**
  String get sendResetCode;

  /// Back to login button text
  ///
  /// In en, this message translates to:
  /// **'Back to Login'**
  String get backToLogin;

  /// Reset code sent success message
  ///
  /// In en, this message translates to:
  /// **'Reset code sent!'**
  String get resetCodeSent;

  /// Reset code sent description with email
  ///
  /// In en, this message translates to:
  /// **'We sent a verification code to {email}'**
  String resetCodeSentDescription(String email);

  /// Continue to verification button text
  ///
  /// In en, this message translates to:
  /// **'Continue to Verification'**
  String get continueToVerification;

  /// Resend code button text
  ///
  /// In en, this message translates to:
  /// **'Resend code'**
  String get resendCode;

  /// Resend code countdown text
  ///
  /// In en, this message translates to:
  /// **'Resend code in {seconds}s'**
  String resendCodeIn(int seconds);

  /// Verify reset code title
  ///
  /// In en, this message translates to:
  /// **'Verify Reset Code'**
  String get verifyResetCode;

  /// Verify reset code description with email
  ///
  /// In en, this message translates to:
  /// **'We sent a 6-digit code to\n{email}'**
  String verifyResetCodeDescription(String email);

  /// Enter verification code title
  ///
  /// In en, this message translates to:
  /// **'Enter Verification Code'**
  String get enterVerificationCode;

  /// Code expiry countdown
  ///
  /// In en, this message translates to:
  /// **'Code expires in {minutes}:{seconds}'**
  String codeExpiresIn(String minutes, String seconds);

  /// Code expired message
  ///
  /// In en, this message translates to:
  /// **'Code has expired'**
  String get codeHasExpired;

  /// Verify code button text
  ///
  /// In en, this message translates to:
  /// **'Verify Code'**
  String get verifyCode;

  /// Resend code when expired button text
  ///
  /// In en, this message translates to:
  /// **'Resend code when expired'**
  String get resendCodeWhenExpired;

  /// Back to email entry button text
  ///
  /// In en, this message translates to:
  /// **'Back to Email Entry'**
  String get backToEmailEntry;

  /// Create new password title
  ///
  /// In en, this message translates to:
  /// **'Create New Password'**
  String get createNewPassword;

  /// Create new password description
  ///
  /// In en, this message translates to:
  /// **'Please create a strong password for your account'**
  String get createNewPasswordDescription;

  /// Reset token expiry message
  ///
  /// In en, this message translates to:
  /// **'Reset token expires in {minutes} minutes'**
  String resetTokenExpiresIn(int minutes);

  /// Reset token expired message
  ///
  /// In en, this message translates to:
  /// **'Reset token has expired. Please request a new password reset.'**
  String get resetTokenExpired;

  /// New password field hint
  ///
  /// In en, this message translates to:
  /// **'Enter your new password'**
  String get enterNewPassword;

  /// Confirm new password field hint
  ///
  /// In en, this message translates to:
  /// **'Confirm your new password'**
  String get confirmNewPasswordHint;

  /// Password requirements section title
  ///
  /// In en, this message translates to:
  /// **'Password Requirements'**
  String get passwordRequirements;

  /// Password requirement: minimum length
  ///
  /// In en, this message translates to:
  /// **'At least 8 characters'**
  String get atLeast8Characters;

  /// Password requirement: lowercase letter
  ///
  /// In en, this message translates to:
  /// **'Contains lowercase letter'**
  String get containsLowercaseLetter;

  /// Password requirement: uppercase letter
  ///
  /// In en, this message translates to:
  /// **'Contains uppercase letter'**
  String get containsUppercaseLetter;

  /// Password requirement: number
  ///
  /// In en, this message translates to:
  /// **'Contains number'**
  String get containsNumber;

  /// Password requirement: special character
  ///
  /// In en, this message translates to:
  /// **'Contains special character'**
  String get containsSpecialCharacter;

  /// Reset password button text
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPasswordButton;

  /// Password validation error for requirements
  ///
  /// In en, this message translates to:
  /// **'Password does not meet requirements'**
  String get passwordDoesNotMeetRequirements;

  /// Empty services list message
  ///
  /// In en, this message translates to:
  /// **'Create your first service to get started'**
  String get createFirstService;

  /// Add service button text
  ///
  /// In en, this message translates to:
  /// **'Add Service'**
  String get addService;

  /// Search locations placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search locations...'**
  String get searchLocations;

  /// Empty locations list message
  ///
  /// In en, this message translates to:
  /// **'Create your first location to get started'**
  String get createFirstLocation;

  /// Add location button text
  ///
  /// In en, this message translates to:
  /// **'Add Location'**
  String get addLocation;

  /// Title for scheduling information section
  ///
  /// In en, this message translates to:
  /// **'Scheduling Information'**
  String get schedulingInformation;

  /// Date label
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// Unknown status text
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get unknown;

  /// Business setup screen title
  ///
  /// In en, this message translates to:
  /// **'Business Setup'**
  String get businessSetup;

  /// Skip setup tooltip
  ///
  /// In en, this message translates to:
  /// **'Skip setup'**
  String get skipSetupTooltip;

  /// Business information section title
  ///
  /// In en, this message translates to:
  /// **'Business Information'**
  String get businessInformation;

  /// Business information section subtitle
  ///
  /// In en, this message translates to:
  /// **'Tell us about your business'**
  String get tellUsAboutYourBusiness;

  /// Business name validation error
  ///
  /// In en, this message translates to:
  /// **'Business name is required'**
  String get businessNameRequired;

  /// Business name minimum length validation error
  ///
  /// In en, this message translates to:
  /// **'Business name must be at least 2 characters'**
  String get businessNameMinLength;

  /// Business name field hint
  ///
  /// In en, this message translates to:
  /// **'Enter your business name'**
  String get enterYourBusinessName;

  /// Business description field label
  ///
  /// In en, this message translates to:
  /// **'Business Description'**
  String get businessDescription;

  /// Business description validation error
  ///
  /// In en, this message translates to:
  /// **'Business description is required'**
  String get businessDescriptionRequired;

  /// Business description minimum length validation error
  ///
  /// In en, this message translates to:
  /// **'Description must be at least 10 characters'**
  String get businessDescriptionMinLength;

  /// Business description field hint
  ///
  /// In en, this message translates to:
  /// **'Describe what your business does'**
  String get describeWhatYourBusinessDoes;

  /// Business category validation error
  ///
  /// In en, this message translates to:
  /// **'Please select a business category'**
  String get businessCategoryRequired;

  /// Short name field label
  ///
  /// In en, this message translates to:
  /// **'Short Name (Optional)'**
  String get shortNameOptional;

  /// Business logo section title
  ///
  /// In en, this message translates to:
  /// **'Business Logo (Optional)'**
  String get businessLogoOptional;

  /// Business logo section description
  ///
  /// In en, this message translates to:
  /// **'Upload your business logo to help customers recognize your brand'**
  String get uploadBusinessLogoDescription;

  /// Upload logo button text
  ///
  /// In en, this message translates to:
  /// **'Click to upload logo'**
  String get clickToUploadLogo;

  /// Business information description text
  ///
  /// In en, this message translates to:
  /// **'This information will be displayed to your customers and used to set up your business profile.'**
  String get businessInfoDescription;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en', 'fr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
    case 'fr':
      return AppLocalizationsFr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}

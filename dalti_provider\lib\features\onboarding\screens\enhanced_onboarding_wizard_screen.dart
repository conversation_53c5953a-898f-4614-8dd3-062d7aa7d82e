import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/onboarding_models.dart';
import '../providers/wizard_navigation_provider.dart';
import '../components/navigation/wizard_progress_indicator.dart';
import '../steps/welcome_step.dart';
import '../steps/enhanced_business_profile_step.dart';
import '../steps/enhanced_location_setup_step.dart';
import '../steps/enhanced_service_creation_step.dart';
import '../steps/enhanced_queue_management_step.dart';
import '../steps/enhanced_summary_step.dart';
import '../../../core/theme/theme_provider.dart';
import '../../../core/routing/app_routes.dart';
import '../../../core/services/local_storage_service.dart';
import '../../../generated/l10n/app_localizations.dart';

/// Enhanced onboarding wizard screen with new navigation framework
/// Supports the new 6-step flow: Welcome → Business → Location → Services → Queues → Summary
class EnhancedOnboardingWizardScreen extends ConsumerStatefulWidget {
  const EnhancedOnboardingWizardScreen({super.key});

  @override
  ConsumerState<EnhancedOnboardingWizardScreen> createState() =>
      _EnhancedOnboardingWizardScreenState();
}

class _EnhancedOnboardingWizardScreenState
    extends ConsumerState<EnhancedOnboardingWizardScreen> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ref.watch(currentThemeProvider);
    final navigationState = ref.watch(wizardNavigationProvider);
    final l10n = AppLocalizations.of(context);

    // Listen to navigation changes and update page controller
    ref.listen<WizardNavigationState>(wizardNavigationProvider, (
      previous,
      current,
    ) {
      if (previous?.currentStepIndex != current.currentStepIndex) {
        _animateToStep(current.currentStepIndex);
      }
    });

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor:
            theme.brightness == Brightness.dark
                ? theme.colorScheme.surface
                : Colors.white,
        surfaceTintColor:
            theme.brightness == Brightness.dark
                ? theme.colorScheme.surface
                : Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.close, color: theme.colorScheme.onSurface),
          onPressed: _handleSkipOnboarding,
          tooltip: l10n.skipSetupTooltip,
        ),
        title: Text(
          l10n.businessSetup,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator (hidden on welcome step)
            if (navigationState.currentStep != OnboardingStep.welcome) ...[
              const WizardStepper(),
            ],

            // Main content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics:
                    const NeverScrollableScrollPhysics(), // Disable swipe navigation
                children: [
                  // Welcome Step
                  const WelcomeStep(),

                  // Business Profile Step
                  const EnhancedBusinessProfileStep(),

                  // Location Setup Step
                  const EnhancedLocationSetupStep(),

                  // Service Creation Step
                  const EnhancedServiceCreationStep(),

                  // Queue Management Step
                  const EnhancedQueueManagementStep(),

                  // Summary Step
                  const EnhancedSummaryStep(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _animateToStep(int stepIndex) {
    if (_pageController.hasClients) {
      _pageController.animateToPage(
        stepIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Handle skip onboarding action
  void _handleSkipOnboarding() async {
    // Set skip status in localStorage
    await LocalStorageService.setOnboardingSkipped(true);

    // Navigate to dashboard
    context.go(AppRoutes.dashboard);
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.celebration, color: Colors.green),
                SizedBox(width: 8),
                Text('Setup Complete!'),
              ],
            ),
            content: const Text(
              'Congratulations! Your Dalti Provider account is now set up and ready to use. '
              'You can start managing your business and serving customers.',
            ),
            actions: [
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushReplacementNamed('/dashboard');
                },
                child: const Text('Go to Dashboard'),
              ),
            ],
          ),
    );
  }
}

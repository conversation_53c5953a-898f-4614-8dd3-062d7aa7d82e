^D:\DALTI-APP-DEV\DALTI-PROVIDER-FLUTTER\DALTI_PROVIDER\BUILD\WINDOWS\X64\CMAKEFILES\3A493342A6B6796E9DEA78E2FB96F6D4\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\src\flutter PROJECT_DIR=D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider FLUTTER_ROOT=C:\src\flutter FLUTTER_EPHEMERAL_DIR=D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral PROJECT_DIR=D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider FLUTTER_TARGET=D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\.dart_tool\package_config.json C:/src/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\DALTI-APP-DEV\DALTI-PROVIDER-FLUTTER\DALTI_PROVIDER\BUILD\WINDOWS\X64\CMAKEFILES\6634D4DE7E08DEB5DB84EC43883CC465\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\DALTI-APP-DEV\DALTI-PROVIDER-FLUTTER\DALTI_PROVIDER\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/windows -BD:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64 --check-stamp-file D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd

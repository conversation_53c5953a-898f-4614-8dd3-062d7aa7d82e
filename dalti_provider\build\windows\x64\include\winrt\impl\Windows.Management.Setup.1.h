// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.220418.1

#pragma once
#ifndef WINRT_Windows_Management_Setup_1_H
#define WINRT_Windows_Management_Setup_1_H
#include "winrt/impl/Windows.Management.Setup.0.h"
WINRT_EXPORT namespace winrt::Windows::Management::Setup
{
    struct __declspec(empty_bases) IAgentProvisioningProgressReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAgentProvisioningProgressReport>
    {
        IAgentProvisioningProgressReport(std::nullptr_t = nullptr) noexcept {}
        IAgentProvisioningProgressReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDeploymentSessionConnectionChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentSessionConnectionChangedEventArgs>
    {
        IDeploymentSessionConnectionChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDeploymentSessionConnectionChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDeploymentSessionHeartbeatRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentSessionHeartbeatRequestedEventArgs>
    {
        IDeploymentSessionHeartbeatRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDeploymentSessionHeartbeatRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDeploymentSessionStateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentSessionStateChangedEventArgs>
    {
        IDeploymentSessionStateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDeploymentSessionStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDeploymentWorkload :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentWorkload>
    {
        IDeploymentWorkload(std::nullptr_t = nullptr) noexcept {}
        IDeploymentWorkload(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDeploymentWorkloadBatch :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentWorkloadBatch>
    {
        IDeploymentWorkloadBatch(std::nullptr_t = nullptr) noexcept {}
        IDeploymentWorkloadBatch(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDeploymentWorkloadBatchFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentWorkloadBatchFactory>
    {
        IDeploymentWorkloadBatchFactory(std::nullptr_t = nullptr) noexcept {}
        IDeploymentWorkloadBatchFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDeploymentWorkloadFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentWorkloadFactory>
    {
        IDeploymentWorkloadFactory(std::nullptr_t = nullptr) noexcept {}
        IDeploymentWorkloadFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDevicePreparationExecutionContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDevicePreparationExecutionContext>
    {
        IDevicePreparationExecutionContext(std::nullptr_t = nullptr) noexcept {}
        IDevicePreparationExecutionContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMachineProvisioningProgressReporter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMachineProvisioningProgressReporter>
    {
        IMachineProvisioningProgressReporter(std::nullptr_t = nullptr) noexcept {}
        IMachineProvisioningProgressReporter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMachineProvisioningProgressReporterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMachineProvisioningProgressReporterStatics>
    {
        IMachineProvisioningProgressReporterStatics(std::nullptr_t = nullptr) noexcept {}
        IMachineProvisioningProgressReporterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif

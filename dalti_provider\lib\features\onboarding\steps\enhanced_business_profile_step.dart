import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/onboarding_models.dart';
import '../providers/onboarding_provider.dart';
import '../components/form_fields/phone_numbers_widget.dart';
import '../components/navigation/wizard_navigation_buttons.dart';
import '../providers/wizard_navigation_provider.dart';
import '../../auth/widgets/hierarchical_category_dropdown.dart';
import '../../auth/models/provider_category.dart';
import '../../../generated/l10n/app_localizations.dart';

/// Enhanced Business Profile step with new navigation framework
class EnhancedBusinessProfileStep extends ConsumerStatefulWidget {
  const EnhancedBusinessProfileStep({super.key});

  @override
  ConsumerState<EnhancedBusinessProfileStep> createState() =>
      _EnhancedBusinessProfileStepState();
}

class _EnhancedBusinessProfileStepState
    extends ConsumerState<EnhancedBusinessProfileStep> {
  final _formKey = GlobalKey<FormState>();
  final _businessNameController = TextEditingController();
  final _shortNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _websiteController = TextEditingController();
  final _logoUrlController = TextEditingController();

  ProviderCategory? _selectedCategory;
  PhoneNumberData _phoneData = const PhoneNumberData();

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _shortNameController.dispose();
    _descriptionController.dispose();
    _websiteController.dispose();
    _logoUrlController.dispose();
    super.dispose();
  }

  /// Load existing business profile data
  void _loadExistingData() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final profile = onboardingState.data?.businessProfile;

    if (profile != null) {
      _businessNameController.text = profile.businessName;
      _shortNameController.text = profile.shortName ?? '';
      _descriptionController.text = profile.description;
      _websiteController.text = profile.website ?? '';
      _logoUrlController.text = profile.logoUrl ?? '';

      // TODO: Load selected category from profile.categoryId
      // This will be handled by the HierarchicalCategoryDropdown

      // Load phone data
      _phoneData = PhoneNumberData(
        mobile: profile.mobile,
        landline: profile.landline,
        fax: profile.fax,
      );

      // Mark step as completed if data exists
      final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
      wizardNotifier.markStepCompleted(OnboardingStep.businessProfile, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Step header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                border: Border(
                  bottom: BorderSide(
                    color: theme.colorScheme.outline.withOpacity(0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.business,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    l10n.businessInformation,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // Form content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Business name field
                      TextFormField(
                        controller: _businessNameController,
                        decoration: InputDecoration(
                          labelText: '${l10n.businessName} *',
                          hintText: l10n.enterYourBusinessName,
                          border: const OutlineInputBorder(),
                          prefixIcon: const Icon(Icons.business),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return l10n.businessNameRequired;
                          }
                          if (value.trim().length < 2) {
                            return l10n.businessNameMinLength;
                          }
                          return null;
                        },
                        onChanged: _onFieldChanged,
                        textCapitalization: TextCapitalization.words,
                      ),

                      const SizedBox(height: 16),

                      // Business description field
                      TextFormField(
                        controller: _descriptionController,
                        decoration: InputDecoration(
                          labelText: '${l10n.businessDescription} *',
                          hintText: l10n.describeWhatYourBusinessDoes,
                          border: const OutlineInputBorder(),
                          prefixIcon: const Icon(Icons.description),
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return l10n.businessDescriptionRequired;
                          }
                          if (value.trim().length < 10) {
                            return l10n.businessDescriptionMinLength;
                          }
                          return null;
                        },
                        onChanged: _onFieldChanged,
                        textCapitalization: TextCapitalization.sentences,
                      ),

                      const SizedBox(height: 16),

                      // Business category dropdown with hierarchical structure
                      HierarchicalCategoryDropdown(
                        selectedCategory: _selectedCategory,
                        decoration: InputDecoration(
                          labelText: '${l10n.businessCategory} *',
                          border: const OutlineInputBorder(),
                          prefixIcon: const Icon(Icons.category),
                        ),
                        onChanged: (category) {
                          setState(() {
                            _selectedCategory = category;
                          });
                          _onFieldChanged('');
                        },
                        validator: (category) {
                          if (category == null) {
                            return l10n.businessCategoryRequired;
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Short name field (optional)
                      TextFormField(
                        controller: _shortNameController,
                        decoration: InputDecoration(
                          labelText: l10n.shortNameOptional,
                          hintText: l10n.shortNameHint,
                          border: const OutlineInputBorder(),
                          prefixIcon: const Icon(Icons.short_text),
                        ),
                        onChanged: _onFieldChanged,
                        textCapitalization: TextCapitalization.words,
                      ),

                      const SizedBox(height: 16),

                      // Logo URL field (optional)
                      TextFormField(
                        controller: _logoUrlController,
                        decoration: const InputDecoration(
                          labelText: 'Logo URL (Optional)',
                          hintText: 'https://example.com/logo.png',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.image),
                        ),
                        keyboardType: TextInputType.url,
                        onChanged: _onFieldChanged,
                      ),

                      const SizedBox(height: 24),

                      // Phone numbers section using reusable component
                      PhoneNumbersWidget(
                        initialData: _phoneData,
                        onChanged: _onPhoneDataChanged,
                        title: 'Contact Information',
                        subtitle:
                            'At least one phone number is required for customer contact',
                        isRequired: true,
                      ),

                      const SizedBox(height: 16),

                      // Website field (optional)
                      TextFormField(
                        controller: _websiteController,
                        decoration: const InputDecoration(
                          labelText: 'Website (Optional)',
                          hintText: 'https://www.yourbusiness.com',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.language),
                        ),
                        keyboardType: TextInputType.url,
                        onChanged: _onFieldChanged,
                      ),

                      const SizedBox(height: 24),

                      // Info card
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: theme.colorScheme.primary.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: theme.colorScheme.primary,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                l10n.businessInfoDescription,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Navigation buttons
            WizardNavigationButtons(
              isValid: _validateForm(),
              validationMessage:
                  _validateForm() ? null : 'Please fill in all required fields',
              onNext: () => _saveAndProceed(),
            ),
          ],
        ),
      ),
    );
  }

  /// Handle field changes to validate and update state
  void _onFieldChanged(String value) {
    setState(() {}); // Trigger rebuild for validation

    // Update navigation state
    final isValid = _validateForm();
    final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
    wizardNotifier.markStepCompleted(OnboardingStep.businessProfile, isValid);

    if (isValid) {
      _saveBusinessProfile();
    }
  }

  /// Handle phone data changes
  void _onPhoneDataChanged(PhoneNumberData phoneData) {
    setState(() {
      _phoneData = phoneData;
    });
    _onFieldChanged(''); // Trigger validation
  }

  /// Validate the form
  bool _validateForm() {
    return _businessNameController.text.trim().isNotEmpty &&
        _businessNameController.text.trim().length >= 2 &&
        _descriptionController.text.trim().isNotEmpty &&
        _descriptionController.text.trim().length >= 10 &&
        _selectedCategory != null &&
        _phoneData.hasPhoneNumber;
  }

  /// Save business profile data
  void _saveBusinessProfile() {
    if (!_validateForm()) return;

    final profile = BusinessProfile(
      businessName: _businessNameController.text.trim(),
      description: _descriptionController.text.trim(),
      categoryId: _selectedCategory!.id,
      categoryName: _selectedCategory!.name,
      shortName:
          _shortNameController.text.trim().isNotEmpty
              ? _shortNameController.text.trim()
              : null,
      logoUrl:
          _logoUrlController.text.trim().isNotEmpty
              ? _logoUrlController.text.trim()
              : null,
      website:
          _websiteController.text.trim().isNotEmpty
              ? _websiteController.text.trim()
              : null,
      mobile: _phoneData.mobile,
      landline: _phoneData.landline,
      fax: _phoneData.fax,
    );

    // Save to onboarding provider
    ref.read(onboardingNotifierProvider.notifier).saveBusinessProfile(profile);
  }

  /// Save and proceed to next step
  void _saveAndProceed() {
    if (_formKey.currentState?.validate() ?? false) {
      _saveBusinessProfile();
      final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
      wizardNotifier.goToNextStep();
    }
  }
}

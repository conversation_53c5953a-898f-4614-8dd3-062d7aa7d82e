/// Generated file. Do not edit.
///
/// Original file: lib/l10n/app_en.arb
/// To regenerate, run: `flutter gen-l10n`
///
/// Localization for Dalti Provider App
/// Supports: English (en), French (fr), Arabic (ar)

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Dalti Provider';

  @override
  String get language => 'Language';

  @override
  String get languageEnglish => 'English';

  @override
  String get languageFrench => 'Français';

  @override
  String get languageArabic => 'العربية';

  @override
  String get settings => 'Settings';

  @override
  String get profile => 'Profile';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get appointments => 'Appointments';

  @override
  String get appointmentDetails => 'Appointment Details';

  @override
  String get calendar => 'Calendar';

  @override
  String get messages => 'Messages';

  @override
  String get customers => 'customers';

  @override
  String get services => 'Services';

  @override
  String get locations => 'Locations';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get languageChanged => 'Language changed successfully';

  @override
  String get languageChangeError =>
      'Failed to change language. Please try again.';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get currentLanguage => 'Current Language';

  @override
  String get login => 'Login';

  @override
  String get logout => 'Logout';

  @override
  String get register => 'Register';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get firstName => 'First Name';

  @override
  String get lastName => 'Last Name';

  @override
  String get phone => 'Phone';

  @override
  String get businessName => 'Business Name';

  @override
  String get add => 'Add';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get update => 'Update';

  @override
  String get create => 'Create';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get refresh => 'Refresh';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get close => 'Close';

  @override
  String get done => 'Done';

  @override
  String get retry => 'Retry';

  @override
  String get noData => 'No data available';

  @override
  String get networkError => 'Network error. Please check your connection.';

  @override
  String get serverError => 'Server error. Please try again later.';

  @override
  String get unknownError => 'An unknown error occurred.';

  @override
  String get validationError => 'Please check your input and try again.';

  @override
  String get required => 'This field is required';

  @override
  String get invalidEmail => 'Please enter a valid email address';

  @override
  String get invalidPhone => 'Please enter a valid phone number';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get helpAndSupport => 'Help & Support';

  @override
  String get theme => 'Theme';

  @override
  String get themeLight => 'Light';

  @override
  String get themeDark => 'Dark';

  @override
  String get themeSystem => 'System';

  @override
  String get logoutConfirmTitle => 'Logout';

  @override
  String get logoutConfirmMessage => 'Are you sure you want to logout?';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get daltiProvider => 'Dalti Provider';

  @override
  String get gettingLocation => 'Getting Location...';

  @override
  String get useCurrentLocation => 'Use Current Location';

  @override
  String get notSpecified => 'Not specified';

  @override
  String get title => 'Title';

  @override
  String get category => 'Category';

  @override
  String get profileInformation => 'Profile Information';

  @override
  String get aboutMe => 'About Me';

  @override
  String get passwordResetSuccessful => 'Password Reset Successful';

  @override
  String get passwordResetSuccessMessage =>
      'Your password has been reset successfully. You can now log in with your new password.';

  @override
  String get goToLogin => 'Go to Login';

  @override
  String get failedToResetPassword =>
      'Failed to reset password. Please try again.';

  @override
  String get forgotPassword => 'Forgot Password';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmNewPassword => 'Confirm New Password';

  @override
  String get enterOtp => 'Enter OTP';

  @override
  String get verifyOtp => 'Verify OTP';

  @override
  String get resendOtp => 'Resend OTP';

  @override
  String get otpSent => 'OTP sent to your email';

  @override
  String get invalidOtp => 'Invalid OTP. Please try again.';

  @override
  String get welcomeBack => 'Welcome Back';

  @override
  String get signInToContinue => 'Sign in to continue';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get signUp => 'Sign Up';

  @override
  String get signIn => 'Sign In';

  @override
  String get appointmentsList => 'Appointments';

  @override
  String get notifications => 'Notifications';

  @override
  String get loginSuccessful => 'Login successful!';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get welcomeToDaltiProvider => 'Welcome to Dalti Provider';

  @override
  String get signInToManageBusiness => 'Sign in to manage your business';

  @override
  String get getStarted => 'Get Started';

  @override
  String get skipSetup => 'Skip Setup?';

  @override
  String get skipSetupMessage =>
      'You can complete your business setup later from the dashboard. However, some features may be limited until setup is complete.';

  @override
  String get continueSetup => 'Continue Setup';

  @override
  String get skipForNow => 'Skip for Now';

  @override
  String get startConversation => 'Start Conversation';

  @override
  String get failedToCreateConversation => 'Failed to create conversation';

  @override
  String get selectCustomer => 'Select a customer';

  @override
  String get initialMessage => 'Initial message (optional):';

  @override
  String get typeMessageHere => 'Type your message here...';

  @override
  String get serviceColor => 'Service Color';

  @override
  String get customerManagement => 'Customer Management';

  @override
  String get comingSoon => 'Coming Soon';

  @override
  String get thisFeatureWillAllowYouTo => 'This feature will allow you to:';

  @override
  String get basicInformation => 'Basic Information';

  @override
  String get contactInformation => 'Contact Information';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get nationalId => 'National ID';

  @override
  String get notes => 'Notes';

  @override
  String get firstNameRequired => 'First name is required';

  @override
  String get lastNameRequired => 'Last name is required';

  @override
  String get pleaseEnterValidEmail => 'Please enter a valid email';

  @override
  String get pleaseEnterValidPhone => 'Please enter a valid phone number';

  @override
  String get customerEmailHint => '<EMAIL>';

  @override
  String get phoneNumberHint => '+213 123 456 789';

  @override
  String get nationalIdHint => 'Optional identification number';

  @override
  String get notesHint => 'Additional notes about the customer';

  @override
  String get activeCustomer => 'Active Customer';

  @override
  String get inactiveCustomer => 'Inactive Customer';

  @override
  String get blockedCustomer => 'Blocked Customer';

  @override
  String get serviceTitle => 'Service Title';

  @override
  String get serviceTitleHint => 'e.g., Consultation, Haircut, Massage';

  @override
  String get serviceTitleRequired => 'Service title is required';

  @override
  String get titleMinLength => 'Title must be at least 2 characters';

  @override
  String get creditPointsRequired => 'Credit Points Required';

  @override
  String get creditPointsHint =>
      'Credits needed to book this service (minimum 1)';

  @override
  String get creditPointsRequiredError =>
      'Credit points requirement is required';

  @override
  String get creditPointsPositive => 'Credit points must be a positive number';

  @override
  String get deleteService => 'Delete Service';

  @override
  String deleteServiceConfirm(String serviceName) {
    return 'Are you sure you want to delete \"$serviceName\"?\n\nThis action cannot be undone.';
  }

  @override
  String get activate => 'Activate';

  @override
  String get deactivate => 'Deactivate';

  @override
  String get minutes => 'min';

  @override
  String serviceDeletedSuccessfully(String serviceName) {
    return 'Service \"$serviceName\" deleted successfully';
  }

  @override
  String failedToDeleteService(String serviceName) {
    return 'Failed to delete service \"$serviceName\"';
  }

  @override
  String get priceNotSet => 'Price not set';

  @override
  String get active => 'Active';

  @override
  String requiresCreditPoints(int points) {
    return 'Requires $points credit points to book';
  }

  @override
  String get hours => 'h';

  @override
  String get inactive => 'Inactive';

  @override
  String get queue => 'Queue';

  @override
  String get queues => 'Queues';

  @override
  String queueCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'queues',
      one: 'queue',
    );
    return '$count $_temp0';
  }

  @override
  String get errorLoadingQueues => 'Error loading queues';

  @override
  String get createLocationsFirstMessage =>
      'Create locations first to manage queues.';

  @override
  String get noQueuesFound => 'No Queues Found';

  @override
  String get noQueuesForLocation =>
      'No queues found for the selected location.';

  @override
  String get createFirstQueue => 'Create your first queue to get started.';

  @override
  String get addQueue => 'Add Queue';

  @override
  String get deleteQueue => 'Delete Queue';

  @override
  String deleteQueueConfirmation(String queueName) {
    return 'Are you sure you want to delete the queue \"$queueName\"?\\n\\nThis action cannot be undone.';
  }

  @override
  String customerCount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'customers',
      one: 'customer',
    );
    return '$count $_temp0';
  }

  @override
  String get clearFilters => 'Clear filters';

  @override
  String get manageCustomerProfiles => 'Manage customer profiles';

  @override
  String get trackCustomerHistory => 'Track customer history';

  @override
  String get sendNotifications => 'Send notifications';

  @override
  String get customerPreferences => 'Customer preferences';

  @override
  String get loyaltyPrograms => 'Loyalty programs';

  @override
  String get call => 'Call';

  @override
  String get sms => 'SMS';

  @override
  String get book => 'Book';

  @override
  String get editCustomer => 'Edit Customer';

  @override
  String get status => 'Status';

  @override
  String get wilaya => 'Wilaya';

  @override
  String get dateAdded => 'Date Added';

  @override
  String get minimumAppointments => 'Minimum Appointments';

  @override
  String get minimumSpent => 'Minimum Spent (DA)';

  @override
  String get selectWilaya => 'Select wilaya';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String get clearAll => 'Clear All';

  @override
  String get searchCustomersPlaceholder => 'Search by name, email, or phone...';

  @override
  String get newCustomer => 'New Customer';

  @override
  String get additionalInformation => 'Additional Information';

  @override
  String get additionalNotesHint => 'Additional notes about the customer';

  @override
  String get customerAlreadyExists => 'Customer Already Exists';

  @override
  String get customerExistsInArchive =>
      'A customer with this information already exists in the archive.';

  @override
  String get wouldYouLikeToRestore =>
      'Would you like to restore the existing customer instead?';

  @override
  String get restoreCustomer => 'Restore Customer';

  @override
  String customerRestoredSuccessfully(String firstName, String lastName) {
    return 'Customer $firstName $lastName restored successfully';
  }

  @override
  String failedToRestoreCustomer(String error) {
    return 'Failed to restore customer: $error';
  }

  @override
  String customerDeletedSuccessfully(String firstName, String lastName) {
    return 'Customer $firstName $lastName deleted successfully';
  }

  @override
  String failedToDeleteCustomer(String error) {
    return 'Failed to delete customer: $error';
  }

  @override
  String customerUpdatedSuccessfully(String firstName, String lastName) {
    return 'Customer $firstName $lastName updated successfully';
  }

  @override
  String failedToUpdateCustomer(String error) {
    return 'Failed to update customer: $error';
  }

  @override
  String get searchAppointmentsPlaceholder => 'Search appointments...';

  @override
  String get filters => 'Filters';

  @override
  String get all => 'All';

  @override
  String get filterAppointments => 'Filter Appointments';

  @override
  String get customizeAppointmentView =>
      'Customize your appointment view with filters';

  @override
  String get queueFilter => 'Queue Filter';

  @override
  String get pending => 'Pending';

  @override
  String get scheduled => 'Scheduled';

  @override
  String get confirmed => 'Confirmed';

  @override
  String get inProgress => 'In Progress';

  @override
  String get completed => 'Completed';

  @override
  String get canceled => 'Canceled';

  @override
  String get noShow => 'No Show';

  @override
  String get rescheduled => 'Rescheduled';

  @override
  String get confirm => 'Confirm';

  @override
  String get startService => 'Start Service';

  @override
  String get complete => 'Complete';

  @override
  String get editAppointment => 'Edit Appointment';

  @override
  String get customerDidNotShowUp => 'Customer did not show up for appointment';

  @override
  String get sessionCompletedSuccessfully => 'Session completed successfully';

  @override
  String get dateRange => 'Date Range';

  @override
  String get startDate => 'Start Date';

  @override
  String get endDate => 'End Date';

  @override
  String get statusFilter => 'Status Filter';

  @override
  String get quickSelection => 'Quick Selection';

  @override
  String get today => 'Today';

  @override
  String get thisWeek => 'This Week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get customRange => 'Custom Range';

  @override
  String get selectStartDate => 'Select Start Date';

  @override
  String get selectEndDate => 'Select End Date';

  @override
  String get clear => 'Clear';

  @override
  String get statusLabel => 'Status';

  @override
  String get dateRangeLabel => 'Date Range';

  @override
  String get queueLabel => 'Queue';

  @override
  String get any => 'Any';

  @override
  String get customerInformation => 'Customer Information';

  @override
  String get serviceDetails => 'Service Details';

  @override
  String get scheduling => 'Scheduling';

  @override
  String get customerRequired => 'Customer *';

  @override
  String get serviceRequired => 'Service *';

  @override
  String get locationRequired => 'Location *';

  @override
  String get queueRequired => 'Queue *';

  @override
  String get dateRequired => 'Date *';

  @override
  String get timeRangeRequired => 'Time Range *';

  @override
  String get selectService => 'Select Service';

  @override
  String get selectLocation => 'Select Location';

  @override
  String get selectQueue => 'Select Queue';

  @override
  String get selectLocationFirst => 'Select a location first';

  @override
  String get appointmentDate => 'Appointment Date';

  @override
  String get startTime => 'Start Time';

  @override
  String get endTime => 'End Time';

  @override
  String get notesOptional => 'Notes (Optional)';

  @override
  String get addNotesPlaceholder => 'Add any additional notes...';

  @override
  String get pleaseSelectCustomer => 'Please select a customer';

  @override
  String get pleaseSelectService => 'Please select a service';

  @override
  String get pleaseSelectLocation => 'Please select a location';

  @override
  String get pleaseSelectQueue => 'Please select a queue';

  @override
  String get startTimeBeforeEndTime => 'Start time must be before end time';

  @override
  String get appointmentCreatedSuccessfully =>
      'Appointment created successfully';

  @override
  String get appointmentNumber => 'Appointment #';

  @override
  String get customerLabel => 'Customer';

  @override
  String get serviceLabel => 'Service';

  @override
  String get originalTime => 'Original Time';

  @override
  String get selectStartTime => 'Select start time';

  @override
  String get selectEndTime => 'Select end time';

  @override
  String get pleaseSelectLocationFirst => 'Please select a location first';

  @override
  String get noQueuesAvailable => 'No queues available for selected location';

  @override
  String get errorLoadingLocations => 'Error loading locations';

  @override
  String get appointmentNotesOptional => 'Appointment notes (optional)';

  @override
  String get duration => 'Duration';

  @override
  String get time => 'Time';

  @override
  String get statusScheduled => 'Scheduled';

  @override
  String get statusConfirmed => 'Confirmed';

  @override
  String get statusCompleted => 'Completed';

  @override
  String get statusCanceled => 'Canceled';

  @override
  String get statusNoShow => 'No Show';

  @override
  String get cancelAppointment => 'Cancel Appointment';

  @override
  String get cancelAppointmentConfirmation =>
      'Are you sure you want to cancel this appointment?';

  @override
  String get cancellationReason => 'Cancellation reason (optional)';

  @override
  String get cancellationReasonHint => 'Enter reason for cancellation...';

  @override
  String get keepAppointment => 'Keep Appointment';

  @override
  String get appointmentCancelledSuccessfully =>
      'Appointment cancelled successfully';

  @override
  String get failedToCancelAppointment => 'Failed to cancel appointment';

  @override
  String get calendarSettings => 'Calendar Settings';

  @override
  String get configureCalendarDisplayPreferences =>
      'Configure calendar display preferences';

  @override
  String get timeSlotInterval => 'Time Slot Interval';

  @override
  String get selectInterval => 'Select interval';

  @override
  String get timeSlotIntervalDescription =>
      'This setting determines how many time slots each hour is divided into. For example, 5 minutes will create 12 slots per hour, while 15 minutes creates 4 slots per hour.';

  @override
  String get apply => 'Apply';

  @override
  String get statusAndOverview => 'Status & Overview';

  @override
  String get serviceInformation => 'Service Information';

  @override
  String get primaryLocation => 'Primary Location';

  @override
  String get cancelAppointmentConfirm => 'Cancel Appointment';

  @override
  String cancelAppointmentMessage(String customerName) {
    return 'Are you sure you want to cancel the appointment with $customerName?';
  }

  @override
  String get completeAppointmentTitle => 'Complete Appointment';

  @override
  String completeAppointmentMessage(String customerName) {
    return 'Mark the appointment with $customerName as completed?';
  }

  @override
  String appointmentConfirmedFor(String customerName) {
    return 'Appointment confirmed for $customerName';
  }

  @override
  String appointmentCanceledFor(String customerName) {
    return 'Appointment with $customerName canceled';
  }

  @override
  String get customer => 'Customer';

  @override
  String get service => 'Service';

  @override
  String get location => 'Location';

  @override
  String get profileOverview => 'Profile Overview';

  @override
  String get statistics => 'Statistics';

  @override
  String get rating => 'Rating';

  @override
  String get reviews => 'Reviews';

  @override
  String get setupStatus => 'Setup Status';

  @override
  String get incomplete => 'Incomplete';

  @override
  String get businessLogo => 'Business Logo';

  @override
  String get provider => 'Provider';

  @override
  String get verified => 'Verified';

  @override
  String get changeLogo => 'Change Logo';

  @override
  String get uploadLogo => 'Upload Logo';

  @override
  String get supportedFormats =>
      'Supported formats: PNG, JPG, JPEG • Max size: 5MB';

  @override
  String get noLogoUploaded => 'No logo uploaded';

  @override
  String get uploadBusinessLogo => 'Upload Business Logo';

  @override
  String get errorLoadingProfile => 'Error Loading Profile';

  @override
  String get profileAndBusinessImages => 'Profile & Business Images';

  @override
  String get professionalTitle => 'Professional Title';

  @override
  String get actions => 'Actions';

  @override
  String get profilePicture => 'Profile Picture';

  @override
  String get titleRequired => 'Title is required';

  @override
  String get phoneRequired => 'Phone number is required';

  @override
  String get noCategorySelected => 'No category selected';

  @override
  String get categoryCannotBeChanged => 'Category cannot be changed';

  @override
  String get saving => 'Saving...';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get profileUpdatedSuccessfully => 'Profile updated successfully';

  @override
  String failedToUpdateProfile(String error) {
    return 'Failed to update profile: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully =>
      'Profile picture updated successfully!';

  @override
  String get profileCompletion => 'Profile Completion';

  @override
  String get overallProgress => 'Overall Progress';

  @override
  String percentComplete(int percentage) {
    return '$percentage% Complete';
  }

  @override
  String get details => 'Details';

  @override
  String get profilePictureItem => 'Profile Picture';

  @override
  String get providerInfo => 'Provider Info';

  @override
  String get nextSteps => 'Next Steps';

  @override
  String get failedToLoadProfileCompletion =>
      'Failed to load profile completion';

  @override
  String get uploadProfessionalProfilePicture =>
      'Upload a professional profile picture';

  @override
  String get completeProviderInformation =>
      'Complete provider information: business description';

  @override
  String get addAtLeastOneServiceLocation =>
      'Add at least one service location';

  @override
  String get createAtLeastOneServiceOffering =>
      'Create at least one service offering';

  @override
  String get setUpBookingQueuesTimeSlots => 'Set up booking queues/time slots';

  @override
  String get customerProfile => 'Customer Profile';

  @override
  String get deleteCustomer => 'Delete Customer';

  @override
  String deleteCustomerConfirmation(String firstName, String lastName) {
    return 'Are you sure you want to delete $firstName $lastName? This action cannot be undone.';
  }

  @override
  String get information => 'Information';

  @override
  String get mobile => 'Mobile';

  @override
  String get customerSince => 'Customer Since';

  @override
  String get customerStatistics => 'Customer Statistics';

  @override
  String get totalSpent => 'Total Spent';

  @override
  String get lastVisit => 'Last Visit';

  @override
  String get never => 'Never';

  @override
  String get recentAppointments => 'Recent Appointments';

  @override
  String get noRecentAppointments => 'No recent appointments';

  @override
  String get addAppointmentFeatureComingSoon =>
      'Add appointment feature coming soon';

  @override
  String get loadingDashboard => 'Loading dashboard...';

  @override
  String get failedToLoadDashboard => 'Failed to load dashboard';

  @override
  String get unknownErrorOccurred => 'Unknown error occurred';

  @override
  String get refreshingData => 'Refreshing data...';

  @override
  String get failedToLoadActiveSessions => 'Failed to load active sessions';

  @override
  String get failedToLoadPendingAppointments =>
      'Failed to load pending appointments';

  @override
  String get processingEmergencyControl => 'Processing emergency control...';

  @override
  String get refreshService => 'Refresh Service';

  @override
  String get editService => 'Edit Service';

  @override
  String get locationName => 'Location Name';

  @override
  String get locationNameHint => 'e.g., Main Office, Branch Downtown';

  @override
  String get locationNameRequired => 'Location name is required';

  @override
  String get locationNameMinLength =>
      'Location name must be at least 2 characters';

  @override
  String get locationNameMaxLength =>
      'Location name must be less than 100 characters';

  @override
  String get streetAddress => 'Street Address';

  @override
  String get streetAddressHint => 'e.g., 123 Main Street';

  @override
  String get streetAddressRequired => 'Street address is required';

  @override
  String get pleaseEnterCompleteAddress => 'Please enter a complete address';

  @override
  String get addressMaxLength => 'Address must be less than 200 characters';

  @override
  String get country => 'Country';

  @override
  String get algeria => 'Algeria';

  @override
  String get monday => 'Monday';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get thursday => 'Thursday';

  @override
  String get friday => 'Friday';

  @override
  String get saturday => 'Saturday';

  @override
  String get sunday => 'Sunday';

  @override
  String get activeServiceSessions => 'Active Service Sessions';

  @override
  String get viewAll => 'View All';

  @override
  String get noActiveSessions => 'No Active Sessions';

  @override
  String get noActiveSessionsDescription =>
      'There are no active service sessions at the moment';

  @override
  String get pendingAppointments => 'Pending Appointments';

  @override
  String get allCaughtUp => 'All Caught Up!';

  @override
  String get noPendingAppointments => 'No pending appointments to review';

  @override
  String get todaysSchedule => 'Today\'s Schedule';

  @override
  String get unableToRefreshData =>
      'Unable to refresh data. Showing cached information.';

  @override
  String get goodMorning => 'Good morning';

  @override
  String get goodAfternoon => 'Good afternoon';

  @override
  String get goodEvening => 'Good evening';

  @override
  String get waiting => 'waiting';

  @override
  String moreQueues(int count) {
    return '+$count more queues';
  }

  @override
  String get noActiveQueues => 'No active queues';

  @override
  String get total => 'Total';

  @override
  String get upcoming => 'Upcoming';

  @override
  String appointmentsAwaitingAction(int count, String plural) {
    return '$count appointment$plural awaiting action';
  }

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get newService => 'New Service';

  @override
  String get newQueue => 'New Queue';

  @override
  String get newAppointment => 'New Appointment';

  @override
  String get noUpcomingAppointments => 'No upcoming appointments';

  @override
  String get allAppointmentsCompleted =>
      'All appointments for today are completed';

  @override
  String get todaysSummary => 'Today\'s Summary';

  @override
  String updatedSecondsAgo(int seconds) {
    return 'Updated ${seconds}s ago';
  }

  @override
  String updatedMinutesAgo(int minutes) {
    return 'Updated ${minutes}m ago';
  }

  @override
  String updatedHoursAgo(int hours) {
    return 'Updated ${hours}h ago';
  }

  @override
  String updatedDaysAgo(int days) {
    return 'Updated ${days}d ago';
  }

  @override
  String get justNow => 'Just now';

  @override
  String get dataRefresh => 'Data Refresh';

  @override
  String lastUpdated(String timeAgo) {
    return 'Last updated: $timeAgo';
  }

  @override
  String get byLocation => 'By Location';

  @override
  String get allQueues => 'All Queues';

  @override
  String get scheduleManagement => 'Schedule Management';

  @override
  String get weeklyView => 'Weekly View';

  @override
  String get listView => 'List View';

  @override
  String get appSettings => 'App Settings';

  @override
  String get account => 'Account';

  @override
  String get management => 'Management';

  @override
  String get manageServiceOfferings => 'Manage your service offerings';

  @override
  String get configureBusinessLocations => 'Configure business locations';

  @override
  String get setupAppointmentQueues => 'Set up appointment queues';

  @override
  String get getHelpAndSupport => 'Get help and support';

  @override
  String get signOutOfAccount => 'Sign out of your account';

  @override
  String get queueManagement => 'Queue Management';

  @override
  String get createService => 'Create Service';

  @override
  String get createNewService => 'Create New Service';

  @override
  String get addNewServiceDescription =>
      'Add a new service to your business offerings';

  @override
  String get serviceAvailableInfo =>
      'Your service will be available for booking once created. You can edit or disable it later from the services list.';

  @override
  String get creatingService => 'Creating Service...';

  @override
  String get description => 'Description';

  @override
  String get describeYourService => 'Describe your service';

  @override
  String get minutesShort => 'm';

  @override
  String get hoursShort => 'h';

  @override
  String get createLocation => 'Create Location';

  @override
  String get nameMinLength => 'Name must be at least 2 characters';

  @override
  String get nameMaxLength => 'Name must be less than 100 characters';

  @override
  String get shortName => 'Short Name';

  @override
  String get shortNameHint => 'e.g., ABC Clinic, XYZ Salon';

  @override
  String get shortNameMaxLength =>
      'Short name must be less than 100 characters';

  @override
  String get city => 'City';

  @override
  String get cityRequired => 'City is required';

  @override
  String get selectValidCity => 'Please select a valid Algerian city';

  @override
  String get selectAlgerianCity => 'Select an Algerian city';

  @override
  String get countryRequired => 'Country is required';

  @override
  String get onlyAvailableInAlgeria => 'Currently only available in Algeria';

  @override
  String locationCreatedSuccessfully(String locationName) {
    return 'Location \"$locationName\" created successfully';
  }

  @override
  String get failedToCreateLocation => 'Failed to create location';

  @override
  String locationUpdatedSuccessfully(String locationName) {
    return 'Location \"$locationName\" updated successfully';
  }

  @override
  String get failedToUpdateLocation => 'Failed to update location';

  @override
  String get createNewLocation => 'Create New Location';

  @override
  String get addNewBusinessLocation =>
      'Add a new business location with address and operating hours';

  @override
  String get locationInformation => 'Location Information';

  @override
  String get address => 'Address';

  @override
  String get addressHint => 'e.g., 123 Main Street, Building A';

  @override
  String get addressRequired => 'Address is required';

  @override
  String get postalCode => 'Postal Code';

  @override
  String get postalCodeHint => 'e.g., 16000 (optional)';

  @override
  String get timezone => 'Timezone';

  @override
  String get timezoneRequired => 'Timezone is required';

  @override
  String get locationCoordinates => 'Location Coordinates';

  @override
  String get getCurrentLocationDescription =>
      'Get your current location to help customers find you easily.';

  @override
  String get latitude => 'Latitude';

  @override
  String get longitude => 'Longitude';

  @override
  String get willBeFilledAutomatically => 'Will be filled automatically';

  @override
  String get pleaseGetCurrentLocation => 'Please get current location';

  @override
  String get invalidLatitude => 'Invalid latitude';

  @override
  String get invalidLongitude => 'Invalid longitude';

  @override
  String get latitudeMustBeBetween => 'Latitude must be between -90 and 90';

  @override
  String get longitudeMustBeBetween => 'Longitude must be between -180 and 180';

  @override
  String get getCurrentLocation => 'Get Current Location';

  @override
  String get amenities => 'Amenities';

  @override
  String get parkingAvailable => 'Parking Available';

  @override
  String get onsiteParkingForCustomers => 'On-site parking for customers';

  @override
  String get elevatorAccess => 'Elevator Access';

  @override
  String get buildingHasElevatorAccess => 'Building has elevator access';

  @override
  String get wheelchairAccessible => 'Wheelchair Accessible';

  @override
  String get accessibleForPeopleWithDisabilities =>
      'Accessible for people with disabilities';

  @override
  String get openingHours => 'Opening Hours';

  @override
  String get closed => 'Closed';

  @override
  String get open => 'Open';

  @override
  String get tipToggleSwitchDayOpenClosed =>
      'Tip: Toggle the switch to mark a day as open or closed';

  @override
  String get locationWillBeAddedToBusinessLocations =>
      'This location will be added to your business locations. You can manage all locations from the locations section';

  @override
  String get locationAvailableForQueueManagement =>
      'Your location will be available for queue and appointment management once created. Make sure to set accurate coordinates and operating hours';

  @override
  String get creatingLocation => 'Creating Location...';

  @override
  String get editLocation => 'Edit Location';

  @override
  String get updateLocationDetails =>
      'Update location details, address, and operating hours';

  @override
  String get updateCoordinatesDescription =>
      'Update coordinates if the location has moved to help customers find you easily';

  @override
  String get changesWillBeUpdatedAcrossServices =>
      'Changes to this location will be updated across all your business services and queues';

  @override
  String get changesWillBeSavedImmediately =>
      'Changes will be saved immediately and reflected in your location settings. Existing queues and appointments will not be affected';

  @override
  String get savingChanges => 'Saving Changes...';

  @override
  String get updateCurrentLocation => 'Update Current Location';

  @override
  String get fax => 'Fax';

  @override
  String get floor => 'Floor';

  @override
  String get mobileHint => 'e.g., +213 555-123456';

  @override
  String get faxHint => 'e.g., +213 555-123456';

  @override
  String get floorHint => 'e.g., 5th Floor';

  @override
  String get createQueue => 'Create Queue';

  @override
  String get editQueue => 'Edit Queue';

  @override
  String get queueInformation => 'Queue Information';

  @override
  String get queueName => 'Queue Name';

  @override
  String get queueNameHint => 'e.g., General Queue, VIP Queue, Walk-ins';

  @override
  String get queueServices => 'Queue Services';

  @override
  String get selectServicesDescription =>
      'Select services that will be available in this queue';

  @override
  String get selectAll => 'Select All';

  @override
  String servicesSelected(int count, int total) {
    return '$count of $total selected';
  }

  @override
  String get queueNameRequired => 'Queue name is required';

  @override
  String get queueNameMinLength => 'Name must be at least 2 characters';

  @override
  String get queueNameMaxLength => 'Queue name cannot exceed 100 characters';

  @override
  String get loadingServices => 'Loading services...';

  @override
  String get credits => 'credits';

  @override
  String get pleaseSelectAtLeastOneService =>
      'Please select at least one service';

  @override
  String get queueOperatingHours => 'Queue Operating Hours';

  @override
  String get setQueueAvailabilityDescription =>
      'Set when this queue is available for appointments';

  @override
  String get noServicesAvailable => 'No services available';

  @override
  String get createServicesFirstMessage =>
      'Create services first to assign them to queues.';

  @override
  String get addServices => 'Add Services';

  @override
  String get serviceDeliveryType => 'Service Delivery Type';

  @override
  String get atBusinessLocation => 'At Business Location';

  @override
  String get atCustomerLocation => 'At Customer Location';

  @override
  String get bothOptions => 'Both Options';

  @override
  String get selectDeliveryType => 'Please select a delivery type';

  @override
  String get selectRegions => 'Select regions...';

  @override
  String regionsSelected(int count) {
    return '$count region(s) selected';
  }

  @override
  String get customColor => 'Custom Color';

  @override
  String get invalidHexColor => 'Invalid hex color';

  @override
  String editServiceTitle(String serviceName) {
    return 'Edit $serviceName';
  }

  @override
  String updateServiceDescription(String serviceName) {
    return 'Update \"$serviceName\" details and settings';
  }

  @override
  String get changesWillBeSaved =>
      'Changes will be saved immediately and reflected in your service offerings. Existing appointments will not be affected.';

  @override
  String errorLoadingService(String error) {
    return 'Error loading service: $error';
  }

  @override
  String errorRefreshingService(String error) {
    return 'Error refreshing service: $error';
  }

  @override
  String serviceUpdatedSuccessfully(String serviceName) {
    return 'Service \"$serviceName\" updated successfully';
  }

  @override
  String get failedToUpdateService => 'Failed to update service';

  @override
  String errorUpdatingService(String error) {
    return 'Error updating service: $error';
  }

  @override
  String get price => 'Price (DA)';

  @override
  String get priceRequired => 'Price is required';

  @override
  String get enterValidPrice => 'Enter valid price';

  @override
  String get serviceDelivery => 'Service Delivery';

  @override
  String get whereProvideService => 'Where do you provide this service?';

  @override
  String get appearance => 'Appearance';

  @override
  String get serviceOptions => 'Service Options';

  @override
  String get publicService => 'Public Service';

  @override
  String get visibleToAllCustomers => 'Visible to all customers';

  @override
  String get acceptOnlineBookings => 'Accept Online Bookings';

  @override
  String get allowCustomersBookOnline => 'Allow customers to book online';

  @override
  String get acceptNewCustomers => 'Accept New Customers';

  @override
  String get allowNewCustomersBook => 'Allow new customers to book';

  @override
  String get enableNotifications => 'Enable Notifications';

  @override
  String get getNotifiedNewBookings => 'Get notified for new bookings';

  @override
  String get serviceAvailableForBooking => 'Service is available for booking';

  @override
  String get selectServedRegionsError =>
      'Please select served regions for customer location services';

  @override
  String get themeModeLight => 'Light';

  @override
  String get themeModeDark => 'Dark';

  @override
  String get themeModeSystem => 'System';

  @override
  String get searchServices => 'Search services...';

  @override
  String get tryAdjustingSearchTerms => 'Try adjusting your search terms';

  @override
  String get noServicesFound => 'No services found';

  @override
  String get errorLoadingServices => 'Error loading services';

  @override
  String get noLocationsFound => 'No locations found';

  @override
  String get failedToLoadLocations => 'Failed to load locations';

  @override
  String get noLocationsAvailable => 'No Locations Available';

  @override
  String get noCustomersFound => 'No customers found';

  @override
  String get failedToLoadCustomers => 'Failed to load customers';

  @override
  String get addFirstCustomerToGetStarted =>
      'Add your first customer to get started';

  @override
  String get addCustomer => 'Add Customer';

  @override
  String get noAppointmentsFound => 'No appointments found';

  @override
  String get tryAdjustingYourFilters => 'Try adjusting your filters';

  @override
  String get createFirstAppointmentToGetStarted =>
      'Create your first appointment to get started';

  @override
  String get addAppointment => 'Add Appointment';

  @override
  String get noAppointmentsScheduled => 'No appointments scheduled';

  @override
  String get tapPlusButtonToAddAppointment =>
      'Tap the + button to add an appointment';

  @override
  String get tapToChange => 'Tap to change';

  @override
  String get emailOrPhone => 'Email or Phone';

  @override
  String get enterEmailOrPhone => 'Enter your email or phone number';

  @override
  String get pleaseEnterEmailOrPhone => 'Please enter your email or phone';

  @override
  String get pleaseEnterPassword => 'Please enter your password';

  @override
  String get joinDaltiProvider => 'Join Dalti Provider';

  @override
  String get createBusinessAccount => 'Create your business account';

  @override
  String get selectBusinessCategory => 'Select your business category';

  @override
  String get createAccount => 'Create Account';

  @override
  String get pleaseEnterBusinessName => 'Please enter your business name';

  @override
  String get businessCategory => 'Business Category';

  @override
  String get pleaseEnterEmail => 'Please enter your email';

  @override
  String get pleaseEnterPhoneNumber => 'Please enter your phone number';

  @override
  String get pleaseConfirmPassword => 'Please confirm your password';

  @override
  String get resetPasswordDescription =>
      'Enter your email address and we\'ll send you a verification code to reset your password';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get enterEmailAddress => 'Enter your email address';

  @override
  String get pleaseEnterEmailAddress => 'Please enter your email address';

  @override
  String get pleaseEnterValidEmailAddress =>
      'Please enter a valid email address';

  @override
  String get sendResetCode => 'Send Reset Code';

  @override
  String get backToLogin => 'Back to Login';

  @override
  String get resetCodeSent => 'Reset code sent!';

  @override
  String resetCodeSentDescription(String email) {
    return 'We sent a verification code to $email';
  }

  @override
  String get continueToVerification => 'Continue to Verification';

  @override
  String get resendCode => 'Resend code';

  @override
  String resendCodeIn(int seconds) {
    return 'Resend code in ${seconds}s';
  }

  @override
  String get verifyResetCode => 'Verify Reset Code';

  @override
  String verifyResetCodeDescription(String email) {
    return 'We sent a 6-digit code to\n$email';
  }

  @override
  String get enterVerificationCode => 'Enter Verification Code';

  @override
  String codeExpiresIn(String minutes, String seconds) {
    return 'Code expires in $minutes:$seconds';
  }

  @override
  String get codeHasExpired => 'Code has expired';

  @override
  String get verifyCode => 'Verify Code';

  @override
  String get resendCodeWhenExpired => 'Resend code when expired';

  @override
  String get backToEmailEntry => 'Back to Email Entry';

  @override
  String get createNewPassword => 'Create New Password';

  @override
  String get createNewPasswordDescription =>
      'Please create a strong password for your account';

  @override
  String resetTokenExpiresIn(int minutes) {
    return 'Reset token expires in $minutes minutes';
  }

  @override
  String get resetTokenExpired =>
      'Reset token has expired. Please request a new password reset.';

  @override
  String get enterNewPassword => 'Enter your new password';

  @override
  String get confirmNewPasswordHint => 'Confirm your new password';

  @override
  String get passwordRequirements => 'Password Requirements';

  @override
  String get atLeast8Characters => 'At least 8 characters';

  @override
  String get containsLowercaseLetter => 'Contains lowercase letter';

  @override
  String get containsUppercaseLetter => 'Contains uppercase letter';

  @override
  String get containsNumber => 'Contains number';

  @override
  String get containsSpecialCharacter => 'Contains special character';

  @override
  String get resetPasswordButton => 'Reset Password';

  @override
  String get passwordDoesNotMeetRequirements =>
      'Password does not meet requirements';

  @override
  String get createFirstService => 'Create your first service to get started';

  @override
  String get addService => 'Add Service';

  @override
  String get searchLocations => 'Search locations...';

  @override
  String get createFirstLocation => 'Create your first location to get started';

  @override
  String get addLocation => 'Add Location';

  @override
  String get schedulingInformation => 'Scheduling Information';

  @override
  String get date => 'Date';

  @override
  String get unknown => 'Unknown';

  @override
  String get businessSetup => 'Business Setup';

  @override
  String get skipSetupTooltip => 'Skip setup';

  @override
  String get businessInformation => 'Business Information';

  @override
  String get tellUsAboutYourBusiness => 'Tell us about your business';

  @override
  String get businessNameRequired => 'Business name is required';

  @override
  String get businessNameMinLength =>
      'Business name must be at least 2 characters';

  @override
  String get enterYourBusinessName => 'Enter your business name';

  @override
  String get businessDescription => 'Business Description';

  @override
  String get businessDescriptionRequired => 'Business description is required';

  @override
  String get businessDescriptionMinLength =>
      'Description must be at least 10 characters';

  @override
  String get describeWhatYourBusinessDoes => 'Describe what your business does';

  @override
  String get businessCategoryRequired => 'Please select a business category';

  @override
  String get shortNameOptional => 'Short Name (Optional)';

  @override
  String get businessLogoOptional => 'Business Logo (Optional)';

  @override
  String get uploadBusinessLogoDescription =>
      'Upload your business logo to help customers recognize your brand';

  @override
  String get clickToUploadLogo => 'Click to upload logo';

  @override
  String get businessInfoDescription =>
      'This information will be displayed to your customers and used to set up your business profile.';
}

﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CB2236C6-C1A4-366F-94C1-BA7D5653F697}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>firebase_core_plugin</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\plugins\firebase_core\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">firebase_core_plugin.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">firebase_core_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\plugins\firebase_core\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">firebase_core_plugin.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">firebase_core_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\plugins\firebase_core\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">firebase_core_plugin.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">firebase_core_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\cpp_client_wrapper\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;INTERNAL_EXPERIMENTAL=1;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/windows/flutter/ephemeral/.plugin_symlinks/firebase_core/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/windows -BD:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64 --check-stamp-file D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64/plugins/firebase_core/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\plugin_version.h.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\plugins\firebase_core\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/windows/flutter/ephemeral/.plugin_symlinks/firebase_core/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/windows -BD:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64 --check-stamp-file D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64/plugins/firebase_core/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\plugin_version.h.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\plugins\firebase_core\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/windows/flutter/ephemeral/.plugin_symlinks/firebase_core/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/windows -BD:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64 --check-stamp-file D:/DALTI-APP-DEV/dalti-provider-flutter/dalti_provider/build/windows/x64/plugins/firebase_core/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\plugin_version.h.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\plugins\firebase_core\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include\firebase_core\firebase_core_plugin_c_api.h" />
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin_c_api.cpp" />
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin.cpp" />
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin.h" />
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\messages.g.cpp" />
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\messages.g.h" />
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\generated\firebase_core\plugin_version.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{36CE5AB1-395A-376C-8491-EDD546440DD6}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{60E7ED00-316D-33AF-B636-A5BBC8FC5981}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\windows\x64\flutter\flutter_wrapper_plugin.vcxproj">
      <Project>{F1DB9DA3-9727-3E03-BBA1-9423995181CC}</Project>
      <Name>flutter_wrapper_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
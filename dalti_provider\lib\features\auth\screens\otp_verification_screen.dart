import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/routing/app_routes.dart';
import '../models/auth_models.dart';
import '../providers/auth_provider.dart';

class OtpVerificationScreen extends ConsumerStatefulWidget {
  final String email;
  final String? registrationData; // JSON string of registration data

  const OtpVerificationScreen({
    super.key,
    required this.email,
    this.registrationData,
  });

  @override
  ConsumerState<OtpVerificationScreen> createState() =>
      _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends ConsumerState<OtpVerificationScreen> {
  final List<TextEditingController> _otpControllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  String get _otpCode {
    return _otpControllers.map((controller) => controller.text).join();
  }

  void _onOtpChanged(String value, int index) {
    if (value.isNotEmpty && index < 5) {
      _focusNodes[index + 1].requestFocus();
    } else if (value.isEmpty && index > 0) {
      _focusNodes[index - 1].requestFocus();
    }

    // Auto-verify when all digits are entered
    if (_otpCode.length == 6) {
      _handleVerifyOtp();
    }
  }

  Future<void> _handleVerifyOtp() async {
    if (_otpCode.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter the complete OTP'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Check if this is a registration flow
    if (widget.registrationData != null) {
      // Parse registration data and complete registration
      final registrationDataMap = _parseRegistrationData(
        widget.registrationData!,
      );

      final request = ProviderRegistrationRequest(
        otp: _otpCode,
        identifier: widget.email,
        password: registrationDataMap['password'] ?? '',
        firstName: registrationDataMap['firstName'] ?? '',
        lastName: registrationDataMap['lastName'] ?? '',
        providerCategoryId: registrationDataMap['providerCategoryId'] ?? 1,
        businessName: registrationDataMap['businessName'] ?? '',
        phone: registrationDataMap['phone'] ?? '',
        email: widget.email,
      );

      final authNotifier = ref.read(authNotifierProvider.notifier);
      final success = await authNotifier.verifyOtpAndRegister(request);

      if (mounted) {
        if (success) {
          // Navigation will be handled automatically by the router
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Registration successful! Welcome to Dalti Provider.',
              ),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          // Show error from auth provider
          final authState = ref.read(authNotifierProvider);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authState.error ?? 'Verification failed'),
              backgroundColor: Colors.red,
            ),
          );

          // Clear OTP fields on error
          _clearOtpFields();
        }
      }
    } else {
      // This shouldn't happen with the new flow, but handle gracefully
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invalid verification flow. Please register again.'),
            backgroundColor: Colors.red,
          ),
        );
        context.go(AppRoutes.register);
      }
    }
  }

  Map<String, dynamic> _parseRegistrationData(String data) {
    try {
      // Parse the registration data from the URL parameter
      // This is a simple string parsing - in production, you'd use proper JSON
      final decoded = Uri.decodeComponent(data);

      // Extract values using regex patterns
      final emailMatch = RegExp(r"email:\s*([^,}]+)").firstMatch(decoded);
      final firstNameMatch = RegExp(
        r"firstName:\s*([^,}]+)",
      ).firstMatch(decoded);
      final lastNameMatch = RegExp(r"lastName:\s*([^,}]+)").firstMatch(decoded);
      final passwordMatch = RegExp(r"password:\s*([^,}]+)").firstMatch(decoded);
      final categoryMatch = RegExp(
        r"providerCategoryId:\s*(\d+)",
      ).firstMatch(decoded);
      final businessMatch = RegExp(
        r"businessName:\s*([^,}]+)",
      ).firstMatch(decoded);
      final phoneMatch = RegExp(r"phone:\s*([^,}]+)").firstMatch(decoded);

      return {
        'email': emailMatch?.group(1)?.trim() ?? widget.email,
        'firstName': firstNameMatch?.group(1)?.trim() ?? '',
        'lastName': lastNameMatch?.group(1)?.trim() ?? '',
        'password': passwordMatch?.group(1)?.trim() ?? '',
        'providerCategoryId': int.tryParse(categoryMatch?.group(1) ?? '1') ?? 1,
        'businessName': businessMatch?.group(1)?.trim() ?? '',
        'phone': phoneMatch?.group(1)?.trim() ?? '',
      };
    } catch (e) {
      print('[] ');
      // Return default values if parsing fails
      return {
        'email': widget.email,
        'firstName': '',
        'lastName': '',
        'password': '',
        'providerCategoryId': 1,
        'businessName': '',
        'phone': '',
      };
    }
  }

  Future<void> _handleResendOtp() async {
    // For resending, we need to call the email OTP request again
    if (widget.registrationData != null) {
      final registrationDataMap = _parseRegistrationData(
        widget.registrationData!,
      );

      final request = EmailOtpRequest(
        email: widget.email,
        firstName: registrationDataMap['firstName'] ?? '',
        lastName: registrationDataMap['lastName'] ?? '',
        password: registrationDataMap['password'] ?? '',
        isProviderRegistration: true,
      );

      final authNotifier = ref.read(authNotifierProvider.notifier);
      final success = await authNotifier.requestEmailOtp(
        request,
        providerCategoryId: registrationDataMap['providerCategoryId'] ?? 1,
        businessName: registrationDataMap['businessName'] ?? '',
        phone: registrationDataMap['phone'] ?? '',
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('OTP sent successfully! Check your email.'),
              backgroundColor: Colors.green,
            ),
          );
          _clearOtpFields();
        } else {
          // Show error from auth provider
          final authState = ref.read(authNotifierProvider);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authState.error ?? 'Failed to resend OTP'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cannot resend OTP. Please register again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _clearOtpFields() {
    for (var controller in _otpControllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.primary,
      body: SafeArea(
        child: Column(
          children: [
            // Top section with AppBar controls
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  // Back button
                  IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.arrow_back,
                        color: Theme.of(context).colorScheme.onPrimary,
                        size: 20,
                      ),
                    ),
                    onPressed: () => context.go(AppRoutes.register),
                  ),
                ],
              ),
            ),

            // Title and subtitle in green area
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Check Your Email',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                    textAlign: TextAlign.start,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'We sent a verification code to ${widget.email}',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onPrimary.withValues(alpha: 0.9),
                    ),
                    textAlign: TextAlign.start,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // White content container with rounded corners
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(32),
                    topRight: Radius.circular(32),
                  ),
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 32),

                      // OTP Input Fields
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: List.generate(6, (index) {
                          return SizedBox(
                            width: 45,
                            height: 55,
                            child: TextFormField(
                              controller: _otpControllers[index],
                              focusNode: _focusNodes[index],
                              textAlign: TextAlign.center,
                              keyboardType: TextInputType.number,
                              maxLength: 1,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                              decoration: const InputDecoration(
                                counterText: '',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.all(0),
                              ),
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              onChanged: (value) => _onOtpChanged(value, index),
                            ),
                          );
                        }),
                      ),
                      const SizedBox(height: 24),

                      // API Info
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.blue.withValues(alpha: 0.3),
                          ),
                        ),
                        child: const Column(
                          children: [
                            Text(
                              '📧 Check your email for the OTP code',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.blue,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 4),
                            Text(
                              'The code will be sent from the Dalti Provider system',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 32),

                      // Verify Button
                      ElevatedButton(
                        onPressed:
                            authState.isLoading ? null : _handleVerifyOtp,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child:
                            authState.isLoading
                                ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                                : const Text(
                                  'Verify Email',
                                  style: TextStyle(fontSize: 16),
                                ),
                      ),
                      const SizedBox(height: 16),

                      // Resend OTP
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'Didn\'t receive the code? ',
                            style: TextStyle(color: Colors.grey),
                          ),
                          TextButton(
                            onPressed:
                                authState.isLoading ? null : _handleResendOtp,
                            child:
                                authState.isLoading
                                    ? const SizedBox(
                                      height: 16,
                                      width: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    )
                                    : const Text('Resend'),
                          ),
                        ],
                      ),

                      const Spacer(),

                      // Back to Register
                      TextButton(
                        onPressed: () => context.go(AppRoutes.register),
                        child: const Text('Back to Registration'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

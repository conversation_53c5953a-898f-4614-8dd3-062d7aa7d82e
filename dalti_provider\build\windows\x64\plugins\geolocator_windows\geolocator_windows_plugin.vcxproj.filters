﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\geolocator_windows.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\geolocator_plugin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include\geolocator_windows\geolocator_windows.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\geolocator_plugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\geolocator_enums.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{4A4641E1-3D11-3F45-B4CF-2C32B542F532}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{E9887D71-F5D0-3073-BB94-222B01CDC73C}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>

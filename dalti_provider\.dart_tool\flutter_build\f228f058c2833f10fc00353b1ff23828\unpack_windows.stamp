{"inputs": ["C:\\src\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\windows.dart", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.exp", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.lib", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.pdb", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_export.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_messenger.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_plugin_registrar.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_texture_registrar.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\icudtl.dat", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\binary_messenger_impl.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\byte_buffer_streams.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\core_implementations.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\engine_method_result.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_engine.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_view_controller.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_call.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\plugin_registrar.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\readme", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\standard_codec.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\texture_registrar_impl.h"], "outputs": ["D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\flutter_windows.dll", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\flutter_export.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\flutter_messenger.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\flutter_windows.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\icudtl.dat", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h"]}
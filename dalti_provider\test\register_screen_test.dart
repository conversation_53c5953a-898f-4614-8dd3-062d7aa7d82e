import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/auth/models/provider_category.dart';

void main() {

  group('Provider Category Tests', () {

    test('Hierarchical provider categories are available', () {
      final parentCategories = ProviderCategories.getParentCategories();
      final selectableCategories = ProviderCategories.getSelectableCategories();
      final allCategories = ProviderCategories.getAllCategories();

      // Test parent categories
      expect(parentCategories.length, equals(3));
      expect(parentCategories.first.name, equals('Healthcare'));
      expect(parentCategories.first.id, equals(1));
      expect(parentCategories.first.isSelectable, isFalse);

      // Test selectable categories (children only)
      expect(selectableCategories.length, greaterThan(0));
      expect(selectableCategories.every((cat) => cat.isSelectable), isTrue);
      expect(selectableCategories.every((cat) => cat.parentId != null), isTrue);

      // Test all categories (parents + children)
      expect(allCategories.length, greaterThan(parentCategories.length));

      // Test category search (should return only selectable categories)
      final healthcareCategories = ProviderCategories.searchCategories('medical');
      expect(healthcareCategories.isNotEmpty, isTrue);
      expect(healthcareCategories.every((cat) => cat.isSelectable), isTrue);

      // Test get category by ID
      final parentCategory = ProviderCategories.getCategoryById(1);
      expect(parentCategory?.name, equals('Healthcare'));
      expect(parentCategory?.isSelectable, isFalse);

      final childCategory = ProviderCategories.getCategoryById(101);
      expect(childCategory?.name, equals('Medical Clinics'));
      expect(childCategory?.isSelectable, isTrue);
      expect(childCategory?.parentId, equals(1));

      final nonExistentCategory = ProviderCategories.getCategoryById(999);
      expect(nonExistentCategory, isNull);

      // Test selectability check
      expect(ProviderCategories.isCategorySelectable(1), isFalse); // Parent
      expect(ProviderCategories.isCategorySelectable(101), isTrue); // Child

      // Test getting children
      final healthcareChildren = ProviderCategories.getChildrenOf(1);
      expect(healthcareChildren.length, equals(4));
      expect(healthcareChildren.every((cat) => cat.parentId == 1), isTrue);
    });
  });
}
